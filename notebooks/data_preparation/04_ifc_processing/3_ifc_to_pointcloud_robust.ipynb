import os
import sys
import logging
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from collections import defaultdict
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Check for required packages
try:
    import ifcopenshell
    import ifcopenshell.geom
    IFC_SUPPORT = True
    logger.info(f"ifcopenshell version: {ifcopenshell.version}")
except ImportError:
    IFC_SUPPORT = False
    logger.error("ifcopenshell is not installed")

try:
    import open3d as o3d
    O3D_SUPPORT = True
    logger.info(f"Open3D version: {o3d.__version__}")
except ImportError:
    O3D_SUPPORT = False
    logger.warning("Open3D is not installed")

try:
    import trimesh
    TRIMESH_SUPPORT = True
    logger.info(f"Trimesh version: {trimesh.__version__}")
except ImportError:
    TRIMESH_SUPPORT = False
    logger.warning("Trimesh is not installed - install with: pip install trimesh")

# Set up paths
base_path = Path("../../../data/raw/trino_enel/ifc")
output_path = Path("../../../data/processed/trino_enel/ifc_pointclouds")
mesh_output_path = Path("../../../data/processed/meshes")

# Create output directories
output_path.mkdir(parents=True, exist_ok=True)
mesh_output_path.mkdir(parents=True, exist_ok=True)

logger.info(f"IFC files path: {base_path}")
logger.info(f"Point clouds output: {output_path}")
logger.info(f"Meshes output: {mesh_output_path}")

# Find IFC files
ifc_files = list(base_path.glob("*.ifc"))
logger.info(f"Found {len(ifc_files)} IFC files")
for ifc_file in ifc_files:
    logger.info(f"  - {ifc_file.name}")

def extract_mesh_from_ifc_element(element, settings):
    """
    Extract mesh data from a single IFC element
    
    Parameters:
    -----------
    element : IFC element
        The IFC element to process
    settings : ifcopenshell.geom.settings
        Geometry settings
    
    Returns:
    --------
    dict : Mesh data with vertices, faces, and metadata
    """
    try:
        if not element.Representation:
            return None
        
        # Create shape from element
        shape = ifcopenshell.geom.create_shape(settings, element)
        
        if not shape or not shape.geometry:
            return None
        
        # Extract vertices and faces
        verts = shape.geometry.verts
        faces = shape.geometry.faces
        
        if not verts or not faces:
            return None
        
        # Reshape vertices to (N, 3)
        vertices = np.array(verts).reshape(-1, 3)
        
        # Reshape faces to (M, 3) - assuming triangular faces
        faces_array = np.array(faces).reshape(-1, 3)
        
        mesh_data = {
            'vertices': vertices,
            'faces': faces_array,
            'element_type': element.is_a(),
            'element_id': element.GlobalId,
            'element_name': element.Name
        }
        
        return mesh_data
        
    except Exception as e:
        logger.debug(f"Error extracting mesh from element {element.GlobalId}: {e}")
        return None

def sample_points_from_mesh_faces(vertices, faces, points_per_face=10):
    """
    Sample points from mesh faces using barycentric coordinates
    
    Parameters:
    -----------
    vertices : numpy.ndarray
        Mesh vertices (N, 3)
    faces : numpy.ndarray
        Mesh faces (M, 3)
    points_per_face : int
        Number of points to sample per face
    
    Returns:
    --------
    numpy.ndarray : Sampled points (K, 3)
    """
    if len(faces) == 0:
        return np.array([]).reshape(0, 3)
    
    sampled_points = []
    
    for face in faces:
        # Get face vertices
        v0, v1, v2 = vertices[face[0]], vertices[face[1]], vertices[face[2]]
        
        # Generate random barycentric coordinates
        r1 = np.random.random(points_per_face)
        r2 = np.random.random(points_per_face)
        
        # Ensure points are inside triangle
        mask = r1 + r2 > 1
        r1[mask] = 1 - r1[mask]
        r2[mask] = 1 - r2[mask]
        
        # Calculate barycentric coordinates
        u = r1
        v = r2
        w = 1 - u - v
        
        # Sample points using barycentric coordinates
        points = (u[:, np.newaxis] * v0 + 
                 v[:, np.newaxis] * v1 + 
                 w[:, np.newaxis] * v2)
        
        sampled_points.append(points)
    
    if sampled_points:
        return np.vstack(sampled_points)
    else:
        return np.array([]).reshape(0, 3)

def convert_ifc_to_pointcloud_robust(ifc_file_path, element_types=None, 
                                   points_per_face=5, max_elements=None,
                                   save_mesh=False):
    """
    Convert IFC file to point cloud using robust mesh extraction
    
    Parameters:
    -----------
    ifc_file_path : Path
        Path to IFC file
    element_types : list, optional
        List of IFC element types to include
    points_per_face : int
        Number of points to sample per mesh face
    max_elements : int, optional
        Maximum number of elements to process
    save_mesh : bool
        Whether to save intermediate mesh files
    
    Returns:
    --------
    dict : Results with points, labels, and metadata
    """
    if not IFC_SUPPORT:
        logger.error("ifcopenshell is not available")
        return None
    
    try:
        logger.info(f"Loading IFC file: {ifc_file_path.name}")
        ifc_model = ifcopenshell.open(str(ifc_file_path))
        
        # Set up geometry settings
        settings = ifcopenshell.geom.settings()
        settings.set(settings.USE_WORLD_COORDS, True)
        settings.set(settings.WELD_VERTICES, True)
        settings.set(settings.USE_BREP_DATA, False)
        settings.set(settings.SEW_SHELLS, True)
        
        # Get elements to process
        if element_types:
            elements = []
            for element_type in element_types:
                elements.extend(ifc_model.by_type(element_type))
            logger.info(f"Found {len(elements)} elements of types {element_types}")
        else:
            elements = ifc_model.by_type('IfcProduct')
            logger.info(f"Found {len(elements)} IfcProduct elements")
        
        # Limit elements if specified
        if max_elements and len(elements) > max_elements:
            elements = elements[:max_elements]
            logger.info(f"Limited to {max_elements} elements")
        
        return process_elements_to_pointcloud(elements, settings, ifc_file_path,
                                            points_per_face, save_mesh)
        
    except Exception as e:
        logger.error(f"Error converting IFC file: {e}")
        return None

def process_elements_to_pointcloud(elements, settings, ifc_file_path, 
                                 points_per_face, save_mesh):
    """
    Process IFC elements and convert to point cloud
    """
    all_points = []
    all_labels = []
    element_stats = defaultdict(int)
    
    # Element type to label mapping
    element_type_map = {}
    current_label = 0
    
    logger.info(f"Processing {len(elements)} elements...")
    start_time = time.time()
    
    for i, element in enumerate(elements):
        if i % 100 == 0 and i > 0:
            elapsed = time.time() - start_time
            rate = i / elapsed
            eta = (len(elements) - i) / rate if rate > 0 else 0
            logger.info(f"Progress: {i}/{len(elements)} ({i/len(elements)*100:.1f}%) - ETA: {eta:.1f}s")
        
        # Extract mesh from element
        mesh_data = extract_mesh_from_ifc_element(element, settings)
        
        if mesh_data is None:
            continue
        
        element_type = mesh_data['element_type']
        
        # Assign label to element type
        if element_type not in element_type_map:
            element_type_map[element_type] = current_label
            current_label += 1
        
        label = element_type_map[element_type]
        
        # Sample points from mesh faces
        points = sample_points_from_mesh_faces(
            mesh_data['vertices'], 
            mesh_data['faces'], 
            points_per_face
        )
        
        if len(points) > 0:
            all_points.append(points)
            all_labels.extend([label] * len(points))
            element_stats[element_type] += len(points)
        
        # Save mesh if requested
        if save_mesh and TRIMESH_SUPPORT and len(mesh_data['vertices']) > 0:
            save_element_mesh(mesh_data, ifc_file_path, i)
    
    # Combine all points
    if all_points:
        combined_points = np.vstack(all_points)
        combined_labels = np.array(all_labels)
    else:
        combined_points = np.array([]).reshape(0, 3)
        combined_labels = np.array([])
    
    logger.info(f"Generated {len(combined_points)} points from {len(elements)} elements")
    logger.info("Points per element type:")
    for elem_type, count in element_stats.items():
        logger.info(f"  {elem_type}: {count}")
    
    return {
        'points': combined_points,
        'labels': combined_labels,
        'element_type_map': element_type_map,
        'element_stats': dict(element_stats)
    }

def save_element_mesh(mesh_data, ifc_file_path, element_index):
    """
    Save individual element mesh to file
    """
    try:
        if not TRIMESH_SUPPORT:
            return
        
        mesh = trimesh.Trimesh(
            vertices=mesh_data['vertices'],
            faces=mesh_data['faces']
        )
        
        # Create filename
        element_type = mesh_data['element_type']
        filename = f"{ifc_file_path.stem}_{element_type}_{element_index:04d}.obj"
        output_file = mesh_output_path / filename
        
        # Export mesh
        mesh.export(str(output_file))
        
    except Exception as e:
        logger.debug(f"Error saving mesh for element {element_index}: {e}")

def save_pointcloud_with_labels(points, labels, output_file, element_type_map=None):
    """
    Save point cloud with class labels to PLY file
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point coordinates (N, 3)
    labels : numpy.ndarray
        Point labels (N,)
    output_file : Path
        Output file path
    element_type_map : dict, optional
        Mapping from element types to labels
    """
    try:
        if len(points) == 0:
            logger.warning("No points to save")
            return
        
        if O3D_SUPPORT:
            # Use Open3D for standard PLY format
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            
            # Add colors based on labels
            if len(labels) > 0:
                colors = generate_colors_from_labels(labels)
                pcd.colors = o3d.utility.Vector3dVector(colors)
            
            o3d.io.write_point_cloud(str(output_file), pcd)
            logger.info(f"Saved point cloud to {output_file.name}")
        
        # Also save custom PLY with labels
        if len(labels) > 0:
            custom_ply_file = output_file.with_suffix('.labeled.ply')
            save_ply_with_labels(points, labels, custom_ply_file)
        
        # Save element type mapping
        if element_type_map:
            mapping_file = output_file.with_suffix('.mapping.txt')
            with open(mapping_file, 'w') as f:
                for elem_type, label in element_type_map.items():
                    f.write(f"{label}: {elem_type}\n")
            logger.info(f"Saved element type mapping to {mapping_file.name}")
        
    except Exception as e:
        logger.error(f"Error saving point cloud: {e}")

def generate_colors_from_labels(labels):
    """
    Generate colors for point cloud based on labels
    """
    unique_labels = np.unique(labels)
    colors = np.zeros((len(labels), 3))
    
    # Generate distinct colors for each label
    for i, label in enumerate(unique_labels):
        # Use HSV color space for better distinction
        hue = (i * 137.5) % 360  # Golden angle for good distribution
        saturation = 0.7
        value = 0.9
        
        # Convert HSV to RGB
        import colorsys
        rgb = colorsys.hsv_to_rgb(hue/360, saturation, value)
        
        mask = labels == label
        colors[mask] = rgb
    
    return colors

def save_ply_with_labels(points, labels, output_file):
    """
    Save PLY file with custom format including labels
    """
    try:
        with open(output_file, 'w') as f:
            # Write PLY header
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(points)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property int label\n")
            f.write("end_header\n")
            
            # Write vertex data
            for point, label in zip(points, labels):
                f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {int(label)}\n")
        
        logger.info(f"Saved labeled PLY to {output_file.name}")
        
    except Exception as e:
        logger.error(f"Error saving labeled PLY: {e}")

def visualize_pointcloud_3d(points, labels=None, title="Point Cloud", sample_size=20000):
    """
    Visualize point cloud with Open3D
    """
    if not O3D_SUPPORT or len(points) == 0:
        logger.warning("Cannot visualize - Open3D not available or no points")
        return
    
    # Sample points if too many
    if len(points) > sample_size:
        indices = np.random.choice(len(points), sample_size, replace=False)
        sampled_points = points[indices]
        sampled_labels = labels[indices] if labels is not None else None
    else:
        sampled_points = points
        sampled_labels = labels
    
    # Create point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(sampled_points)
    
    # Add colors based on labels
    if sampled_labels is not None:
        colors = generate_colors_from_labels(sampled_labels)
        pcd.colors = o3d.utility.Vector3dVector(colors)
    
    # Visualize
    o3d.visualization.draw_geometries([pcd], window_name=title)

# Convert first IFC file if available
if not IFC_SUPPORT:
    logger.error("Cannot proceed without ifcopenshell")
elif not ifc_files:
    logger.error("No IFC files found")
else:
    ifc_file = ifc_files[0]
    logger.info(f"Converting IFC file: {ifc_file.name}")
    
    # Convert with robust method
    result = convert_ifc_to_pointcloud_robust(
        ifc_file,
        element_types=None,  # Include all element types
        points_per_face=3,   # 3 points per face for good coverage
        max_elements=1000,   # Limit for testing
        save_mesh=False      # Set to True to save intermediate meshes
    )
    
    if result and len(result['points']) > 0:
        logger.info(f"Successfully generated {len(result['points'])} points")
        
        # Save point cloud
        output_file = output_path / f"{ifc_file.stem}_robust_pointcloud.ply"
        save_pointcloud_with_labels(
            result['points'], 
            result['labels'], 
            output_file,
            result['element_type_map']
        )
        
    else:
        logger.error("Failed to convert IFC file or no points generated")

# Visualize the generated point cloud
if 'result' in locals() and result and len(result['points']) > 0:
    logger.info("Visualizing point cloud...")
    
    # 3D visualization with Open3D
    visualize_pointcloud_3d(
        result['points'], 
        result['labels'], 
        "Robust IFC Point Cloud",
        sample_size=15000
    )
    
    # Print statistics
    logger.info("Point cloud statistics:")
    logger.info(f"  Total points: {len(result['points'])}")
    logger.info(f"  Unique labels: {len(np.unique(result['labels']))}")
    logger.info(f"  Element types: {list(result['element_type_map'].keys())}")
    
else:
    logger.warning("No point cloud data available for visualization")

# Process all IFC files
if IFC_SUPPORT and len(ifc_files) > 1:
    logger.info(f"Processing {len(ifc_files)} IFC files in batch mode")
    
    for ifc_file in ifc_files:
        output_file = output_path / f"{ifc_file.stem}_robust_pointcloud.ply"
        
        if output_file.exists():
            logger.info(f"Skipping {ifc_file.name} - output already exists")
            continue
        
        logger.info(f"Processing {ifc_file.name}...")
        
        result = convert_ifc_to_pointcloud_robust(
            ifc_file,
            element_types=None,
            points_per_face=3,
            max_elements=2000,  # Increase for production
            save_mesh=False
        )
        
        if result and len(result['points']) > 0:
            save_pointcloud_with_labels(
                result['points'], 
                result['labels'], 
                output_file,
                result['element_type_map']
            )
            logger.info(f"Completed {ifc_file.name} - {len(result['points'])} points")
        else:
            logger.warning(f"Failed to process {ifc_file.name}")

else:
    logger.info("Batch processing skipped - only one file or ifcopenshell not available")