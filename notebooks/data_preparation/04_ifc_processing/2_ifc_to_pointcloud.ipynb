# Install required packages
# Uncomment and run this cell if you need to install the packages

# !pip install ifcopenshell open3d numpy matplotlib

# Import libraries
import os
import numpy as np
import matplotlib.pyplot as plt
import logging
import ifcopenshell
import ifcopenshell.geom
import open3d as o3d

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# Mount Google Drive if in Colab
try:
    from google.colab import drive
    drive.mount('/content/gdrive')
    IN_COLAB = True
    logger.info("Google Drive mounted successfully.")
except ImportError:
    IN_COLAB = False
    logger.info("Not running in Google Colab. Using local file system.")

def convert_ifc_to_pointcloud(filename, element_types=None, sampling_rate=1.0, save_to_file=None, visualize=False):
    """
    Converts an IFC file to a point cloud with enhanced control options.

    """
    logger.info(f"Converting IFC file to point cloud: {filename}")
    
    if not IFC_SUPPORT:
        logger.error("IFC support is not available. Please install ifcopenshell.")
        return None, None
    
    try:
        # Load IFC file
        ifc_file = ifcopenshell.open(filename)
        
        # Set up geometry settings
        settings = ifcopenshell.geom.settings()
        settings.set(settings.USE_WORLD_COORDS, True)
        
        # Get all products or filter by element types
        if element_types:
            products = []
            for element_type in element_types:
                products.extend(ifc_file.by_type(element_type))
            logger.info(f"Found {len(products)} products of types {element_types}")
        else:
            products = ifc_file.by_type("IfcProduct")
            logger.info(f"Found {len(products)} products in IFC file")
        
        # Extract all vertices from all shapes
        all_vertices = []
        product_colors = []  # For visualization
        
        # Process products
        for product in products:
            if product.Representation:
                try:
                    # Create a shape from the product
                    shape = ifcopenshell.geom.create_shape(settings, product)
                    
                    # Get the vertices
                    verts = shape.geometry.verts
                    if verts:
                        # Reshape to (N, 3) array
                        points = np.array(verts).reshape(-1, 3)
                        
                        # Apply sampling if needed
                        if sampling_rate < 1.0:
                            num_points = max(1, int(points.shape[0] * sampling_rate))
                            indices = np.random.choice(points.shape[0], num_points, replace=False)
                            points = points[indices]
                        
                        all_vertices.append(points)
                        
                        # Get color for visualization
                        try:
                            color = shape.geometry.colors
                            if color:
                                color = np.array(color).reshape(-1, 4)[:, :3]  # RGBA to RGB
                                product_colors.append(np.tile(color[0], (points.shape[0], 1)))
                            else:
                                # Generate a random color for this product
                                random_color = np.random.rand(3)
                                product_colors.append(np.tile(random_color, (points.shape[0], 1)))
                        except:
                            # Generate a random color for this product
                            random_color = np.random.rand(3)
                            product_colors.append(np.tile(random_color, (points.shape[0], 1)))
                except Exception as e:
                    # Skip products that can't be processed
                    logger.warning(f"Skipping product {product.id()}: {str(e)}")
                    continue
        
        if not all_vertices:
            logger.error(f"No geometry found in IFC file: {filename}")
            return None, None
            
        # Combine all vertices into a single array
        combined_vertices = np.vstack(all_vertices)
        combined_colors = np.vstack(product_colors) if product_colors else None
        
        logger.info(f"Extracted {combined_vertices.shape[0]} points from {filename}")
        
        # Visualize if requested
        if visualize and O3D_SUPPORT and combined_vertices.shape[0] > 0:
            visualize_pointcloud(combined_vertices, combined_colors, "IFC Point Cloud")
        
        # Save to file if requested
        if save_to_file and O3D_SUPPORT and combined_vertices.shape[0] > 0:
            save_pointcloud(combined_vertices, save_to_file, combined_colors)
        
        return combined_vertices, combined_colors
    except Exception as e:
        logger.error(f"Error converting IFC file to point cloud: {e}")
        return None, None

def visualize_pointcloud(points, colors=None, title="Point Cloud"):
    """
    Visualizes a point cloud using Open3D.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Array of shape (N, 3) containing XYZ coordinates
    colors : numpy.ndarray, optional
        Array of shape (N, 3) containing RGB colors
    title : str, optional
        Title for the visualization window
    """
    if not O3D_SUPPORT:
        logger.error("Open3D is not available. Cannot visualize point cloud.")
        return
    
    try:
        # Create Open3D point cloud
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Add colors if available
        if colors is not None:
            pcd.colors = o3d.utility.Vector3dVector(colors)
        
        # Visualize
        logger.info(f"Visualizing point cloud with {points.shape[0]} points...")
        o3d.visualization.draw_geometries([pcd], window_name=title)
    except Exception as e:
        logger.error(f"Visualization failed: {str(e)}")

def save_pointcloud(points, filename, colors=None):
    """
    Saves a point cloud to a file.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Array of shape (N, 3) containing XYZ coordinates
    colors : numpy.ndarray, optional
        Array of shape (N, 3) containing RGB colors
    filename : str
        Path to save the point cloud (.ply or .pcd format)
    """
    if not O3D_SUPPORT:
        logger.error("Open3D is not available. Cannot save point cloud.")
        return
    
    try:
        # Create Open3D point cloud
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        # Add colors if available
        if colors is not None:
            pcd.colors = o3d.utility.Vector3dVector(colors)
        
        # Save to file
        if filename.lower().endswith('.ply'):
            o3d.io.write_point_cloud(filename, pcd)
        elif filename.lower().endswith('.pcd'):
            o3d.io.write_point_cloud(filename, pcd)
        else:
            # Default to PLY if no extension or unknown extension
            filename = filename + '.ply' if '.' not in filename else filename
            o3d.io.write_point_cloud(filename, pcd)
        
        logger.info(f"Saved point cloud to {filename}")
    except Exception as e:
        logger.error(f"Failed to save point cloud: {str(e)}")

def list_ifc_element_types(filename):
    """
    Lists all element types in an IFC file and their counts.
    
    Parameters:
    -----------
    filename : str
        Path to the IFC file
        
    Returns:
    --------
    element_types : dict
        Dictionary with element types as keys and counts as values
    """
    if not IFC_SUPPORT:
        logger.error("IFC support is not available. Please install ifcopenshell.")
        return None
    
    try:
        # Load IFC file
        ifc_file = ifcopenshell.open(filename)
        
        # Get all entity types
        entity_types = {}
        for entity in ifc_file.by_type("IfcProduct"):
            entity_type = entity.is_a()
            if entity_type in entity_types:
                entity_types[entity_type] += 1
            else:
                entity_types[entity_type] = 1
        
        # Sort by count (descending)
        sorted_types = dict(sorted(entity_types.items(), key=lambda item: item[1], reverse=True))
        
        # Print summary
        print(f"\nIFC Element Types in {os.path.basename(filename)}:")
        print("-" * 50)
        print(f"{'Element Type':<30} {'Count':>10}")
        print("-" * 50)
        for entity_type, count in sorted_types.items():
            print(f"{entity_type:<30} {count:>10}")
        print("-" * 50)
        print(f"{'Total':<30} {sum(sorted_types.values()):>10}")
        
        return sorted_types
    except Exception as e:
        logger.error(f"Error listing IFC element types: {e}")
        return None

# Define the path to your IFC file
if IN_COLAB:
    # Google Drive path - using the specific path provided
    base_path = '/content/gdrive/MyDrive/pc-experiment'
else:
    # Local path - adjust as needed
    base_path = 'data/pc-experiment'

# IFC file path - using the specific file provided
ifc_file = f"{base_path}/GRE.EEC.S.00.IT.P.14353.00.265.ifc"

# Create output directory if it doesn't exist
output_dir = f"{base_path}/output"
os.makedirs(output_dir, exist_ok=True)

# Output file path for the point cloud
output_file = f"{output_dir}/ifc_pointcloud.ply"

print(f"IFC file: {ifc_file}")
print(f"Output file: {output_file}")

def check_ifc_compatibility(filename):
    """
    Checks if an IFC file is compatible with our conversion process.
    
    Parameters:
    -----------
    filename : str
        Path to the IFC file
        
    Returns:
    --------
    compatibility : dict
        Dictionary with compatibility information
    """
    if not IFC_SUPPORT:
        return {"compatible": False, "reason": "ifcopenshell not installed"}
    
    try:
        # Try to open the IFC file
        ifc_file = ifcopenshell.open(filename)
        
        # Get schema information
        schema = ifc_file.schema
        
        # Count entities
        product_count = len(ifc_file.by_type("IfcProduct"))
        
        # Check for common entity types
        common_types = ["IfcWall", "IfcSlab", "IfcColumn", "IfcBeam", "IfcDoor", "IfcWindow"]
        found_types = []
        for entity_type in common_types:
            if len(ifc_file.by_type(entity_type)) > 0:
                found_types.append(entity_type)
        
        # Check file size
        file_size_mb = os.path.getsize(filename) / (1024 * 1024)
        
        # Determine compatibility
        is_compatible = True
        warnings = []
        
        if product_count == 0:
            is_compatible = False
            warnings.append("No IfcProduct entities found")
        
        if file_size_mb > 100:
            warnings.append(f"Large file size ({file_size_mb:.1f} MB) may cause memory issues")
        
        if not found_types:
            warnings.append("No common building elements found")
        
        # Return compatibility information
        return {
            "compatible": is_compatible,
            "schema": schema,
            "product_count": product_count,
            "found_types": found_types,
            "file_size_mb": file_size_mb,
            "warnings": warnings
        }
    except Exception as e:
        return {"compatible": False, "reason": str(e)}


# Check IFC file compatibility
if IFC_SUPPORT:
    print("\n=== Checking IFC File Compatibility ===\n")
    compatibility = check_ifc_compatibility(ifc_file)
    
    if compatibility["compatible"]:
        print(f"✅ IFC file is compatible")
        print(f"Schema: {compatibility['schema']}")
        print(f"Product Count: {compatibility['product_count']}")
        print(f"File Size: {compatibility['file_size_mb']:.1f} MB")
        print(f"Found Types: {', '.join(compatibility['found_types'])}")
        
        if compatibility['warnings']:
            print("\nWarnings:")
            for warning in compatibility['warnings']:
                print(f"⚠️ {warning}")
    else:
        print(f"❌ IFC file is not compatible: {compatibility.get('reason', 'Unknown reason')}")
        print("Please try a different IFC file.")
else:
    print("IFC support not available. Cannot check compatibility.")

# List all element types in the IFC file
if IFC_SUPPORT:
    element_types = list_ifc_element_types(ifc_file)
else:
    print("IFC support not available. Cannot list element types.")

# Enhanced conversion function with better error handling
def convert_ifc_to_pointcloud_safe(filename, element_types=None, sampling_rate=1.0, save_to_file=None, visualize=False, max_points=10000000):
    """
    Enhanced version of convert_ifc_to_pointcloud with better error handling and progress tracking.
    
    Parameters are the same as convert_ifc_to_pointcloud with the addition of:
    max_points : int, optional
        Maximum number of points to extract (to prevent memory issues)
    """
    logger.info(f"Converting IFC file to point cloud (safe mode): {filename}")
    
    if not IFC_SUPPORT:
        logger.error("IFC support is not available. Please install ifcopenshell.")
        return None, None
    
    # Check compatibility first
    compatibility = check_ifc_compatibility(filename)
    if not compatibility["compatible"]:
        logger.error(f"IFC file is not compatible: {compatibility.get('reason', 'Unknown reason')}")
        return None, None
    
    # Log compatibility information
    logger.info(f"IFC Schema: {compatibility['schema']}")
    logger.info(f"Product Count: {compatibility['product_count']}")
    logger.info(f"File Size: {compatibility['file_size_mb']:.1f} MB")
    
    # Log any warnings
    for warning in compatibility.get("warnings", []):
        logger.warning(f"Compatibility warning: {warning}")
    
    try:
        # Load IFC file
        ifc_file = ifcopenshell.open(filename)
        
        # Set up geometry settings
        settings = ifcopenshell.geom.settings()
        settings.set(settings.USE_WORLD_COORDS, True)
        
        # Get all products or filter by element types
        if element_types:
            products = []
            for element_type in element_types:
                products.extend(ifc_file.by_type(element_type))
            logger.info(f"Found {len(products)} products of types {element_types}")
        else:
            products = ifc_file.by_type("IfcProduct")
            logger.info(f"Found {len(products)} products in IFC file")
        
        # Extract all vertices from all shapes
        all_vertices = []
        product_colors = []  # For visualization
        
        # Track progress and errors
        processed_count = 0
        error_count = 0
        total_points = 0
        
        # Process products with progress tracking
        print(f"Processing {len(products)} products...")
        for i, product in enumerate(products):
            # Print progress every 100 products or 10% of total
            progress_interval = max(1, min(100, len(products) // 10))
            if i % progress_interval == 0 and i > 0:
                print(f"Progress: {i}/{len(products)} products ({i/len(products)*100:.1f}%), {total_points} points")
            
            # Check if we've exceeded the maximum point count
            if total_points > max_points:
                logger.warning(f"Reached maximum point count ({max_points}). Stopping extraction.")
                break
                
            if product.Representation:
                try:
                    # Create a shape from the product
                    shape = ifcopenshell.geom.create_shape(settings, product)
                    
                    # Get the vertices
                    verts = shape.geometry.verts
                    if verts:
                        # Reshape to (N, 3) array
                        points = np.array(verts).reshape(-1, 3)
                        
                        # Apply sampling if needed
                        if sampling_rate < 1.0:
                            num_points = max(1, int(points.shape[0] * sampling_rate))
                            indices = np.random.choice(points.shape[0], num_points, replace=False)
                            points = points[indices]
                        
                        all_vertices.append(points)
                        total_points += points.shape[0]
                        
                        # Get color for visualization
                        try:
                            color = shape.geometry.colors
                            if color:
                                color = np.array(color).reshape(-1, 4)[:, :3]  # RGBA to RGB
                                product_colors.append(np.tile(color[0], (points.shape[0], 1)))
                            else:
                                # Generate a random color for this product
                                random_color = np.random.rand(3)
                                product_colors.append(np.tile(random_color, (points.shape[0], 1)))
                        except:
                            # Generate a random color for this product
                            random_color = np.random.rand(3)
                            product_colors.append(np.tile(random_color, (points.shape[0], 1)))
                        
                        processed_count += 1
                except Exception as e:
                    # Skip products that can't be processed
                    error_count += 1
                    if error_count < 10:  # Only log the first few errors to avoid spam
                        logger.warning(f"Skipping product {product.id()}: {str(e)}")
                    elif error_count == 10:
                        logger.warning("Too many errors, suppressing further error messages...")
                    continue
        
        # Log processing summary
        print(f"Processing complete: {processed_count} products processed, {error_count} errors")
        
        if not all_vertices:
            logger.error(f"No geometry found in IFC file: {filename}")
            return None, None
            
        # Combine all vertices into a single array
        combined_vertices = np.vstack(all_vertices)
        combined_colors = np.vstack(product_colors) if product_colors else None
        
        logger.info(f"Extracted {combined_vertices.shape[0]} points from {filename}")
        
        # Check for NaN or Inf values
        if np.isnan(combined_vertices).any() or np.isinf(combined_vertices).any():
            logger.warning("Found NaN or Inf values in point cloud. Cleaning...")
            valid_mask = ~(np.isnan(combined_vertices).any(axis=1) | np.isinf(combined_vertices).any(axis=1))
            combined_vertices = combined_vertices[valid_mask]
            if combined_colors is not None:
                combined_colors = combined_colors[valid_mask]
            logger.info(f"After cleaning: {combined_vertices.shape[0]} points")
        
        # Visualize if requested
        if visualize and O3D_SUPPORT and combined_vertices.shape[0] > 0:
            visualize_pointcloud(combined_vertices, combined_colors, "IFC Point Cloud")
        
        # Save to file if requested
        if save_to_file and O3D_SUPPORT and combined_vertices.shape[0] > 0:
            save_pointcloud(combined_vertices, save_to_file, combined_colors)
        
        return combined_vertices, combined_colors
    except MemoryError:
        logger.error(f"Memory error while processing IFC file. Try reducing sampling_rate or max_points.")
        return None, None
    except Exception as e:
        logger.error(f"Error converting IFC file to point cloud: {e}")
        return None, None

# Convert IFC to point cloud with all elements using the enhanced safe conversion
if IFC_SUPPORT:
    print("\n=== Converting IFC to Point Cloud (All Elements) ===\n")
    
    # First check if the file is very large
    compatibility = check_ifc_compatibility(ifc_file)
    file_size_mb = compatibility.get('file_size_mb', 0)
    
    # Adjust sampling rate based on file size
    if file_size_mb > 200:  # Very large file
        sampling_rate = 0.1  # Use 10% of points
        print(f"File is very large ({file_size_mb:.1f} MB), using 10% sampling rate")
    elif file_size_mb > 50:  # Medium-large file
        sampling_rate = 0.5  # Use 50% of points
        print(f"File is large ({file_size_mb:.1f} MB), using 50% sampling rate")
    else:  # Small file
        sampling_rate = 1.0  # Use all points
        print(f"File is small ({file_size_mb:.1f} MB), using 100% sampling rate")
    
    # Convert with safe mode
    points, colors = convert_ifc_to_pointcloud_safe(
        ifc_file,
        element_types=None,  # Include all element types
        sampling_rate=sampling_rate,
        save_to_file=output_file,
        visualize=True,      # Visualize the point cloud
        max_points=5000000   # Limit to 5 million points to prevent memory issues
    )
    
    if points is not None:
        print(f"Successfully converted IFC to point cloud with {points.shape[0]} points")
        print(f"Saved point cloud to: {output_file}")
        
        # If we have a lot of points, also create a downsampled version
        if points.shape[0] > 100000:  # More than 100k points
            print("\nCreating downsampled version for easier handling...")
            
            # Create Open3D point cloud
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            if colors is not None:
                pcd.colors = o3d.utility.Vector3dVector(colors)
            
            # Apply voxel downsampling
            voxel_size = 0.05  # Adjust based on your model size
            pcd_down = pcd.voxel_down_sample(voxel_size=voxel_size)
            
            # Save voxel downsampled point cloud
            voxel_output_file = output_file.replace('.ply', '_voxel_downsampled.ply')
            o3d.io.write_point_cloud(voxel_output_file, pcd_down)
            
            # Display results
            print(f"Applied voxel downsampling with voxel size {voxel_size}")
            print(f"Original points: {points.shape[0]}, After voxel downsampling: {len(pcd_down.points)}")
            print(f"Saved voxel downsampled point cloud to: {voxel_output_file}")
    else:
        print("Failed to convert IFC to point cloud")
else:
    print("IFC support not available. Cannot convert IFC file to point cloud.")

# Convert specific IFC elements to point cloud
if IFC_SUPPORT:
    # First, list all element types in the IFC file to see what's available
    print("\n=== Analyzing IFC File Element Types ===\n")
    element_types_dict = list_ifc_element_types(ifc_file)
    
    # Select specific element types based on what's in the file
    # We'll create multiple point clouds with different element types
    
    # 1. Structural elements
    structural_types = ["IfcWall", "IfcSlab", "IfcColumn", "IfcBeam", "IfcFooting", "IfcPile", "IfcRoof"]
    # Filter to only include types that exist in the file
    structural_types = [t for t in structural_types if t in element_types_dict]
    
    if structural_types:
        print(f"\n=== Converting Structural Elements to Point Cloud: {structural_types} ===\n")
        
        # Output file for structural elements
        structural_output_file = f"{output_dir}/ifc_structural_elements.ply"
        
        points, colors = convert_ifc_to_pointcloud(
            ifc_file,
            element_types=structural_types,
            sampling_rate=1.0,
            save_to_file=structural_output_file,
            visualize=True
        )
        
        if points is not None:
            print(f"Successfully converted structural elements to point cloud with {points.shape[0]} points")
            print(f"Saved point cloud to: {structural_output_file}")
        else:
            print("Failed to convert structural elements to point cloud")
    else:
        print("No structural elements found in the IFC file")
    
    # 2. MEP elements (Mechanical, Electrical, Plumbing)
    mep_types = ["IfcPipe", "IfcDuctSegment", "IfcCableSegment", "IfcFlowTerminal", "IfcFlowController", "IfcFlowFitting"]
    # Filter to only include types that exist in the file
    mep_types = [t for t in mep_types if t in element_types_dict]
    
    if mep_types:
        print(f"\n=== Converting MEP Elements to Point Cloud: {mep_types} ===\n")
        
        # Output file for MEP elements
        mep_output_file = f"{output_dir}/ifc_mep_elements.ply"
        
        points, colors = convert_ifc_to_pointcloud(
            ifc_file,
            element_types=mep_types,
            sampling_rate=1.0,
            save_to_file=mep_output_file,
            visualize=True
        )
        
        if points is not None:
            print(f"Successfully converted MEP elements to point cloud with {points.shape[0]} points")
            print(f"Saved point cloud to: {mep_output_file}")
        else:
            print("Failed to convert MEP elements to point cloud")
    else:
        print("No MEP elements found in the IFC file")
    
    # 3. Custom selection - choose the top 3 most numerous element types
    top_types = list(element_types_dict.keys())[:3]
    
    if top_types:
        print(f"\n=== Converting Top 3 Most Common Element Types to Point Cloud: {top_types} ===\n")
        
        # Output file for top elements
        top_output_file = f"{output_dir}/ifc_top_elements.ply"
        
        points, colors = convert_ifc_to_pointcloud(
            ifc_file,
            element_types=top_types,
            sampling_rate=1.0,
            save_to_file=top_output_file,
            visualize=True
        )
        
        if points is not None:
            print(f"Successfully converted top element types to point cloud with {points.shape[0]} points")
            print(f"Saved point cloud to: {top_output_file}")
        else:
            print("Failed to convert top element types to point cloud")
else:
    print("IFC support not available. Cannot convert IFC file to point cloud.")

# Convert IFC to downsampled point clouds with different sampling rates
if IFC_SUPPORT:
    print("\n=== Converting IFC to Downsampled Point Clouds ===\n")
    
    # Try different sampling rates
    sampling_rates = [0.5, 0.1, 0.01]
    
    for rate in sampling_rates:
        print(f"\n--- Sampling Rate: {rate*100}% ---")
        
        # Output file for downsampled point cloud
        downsampled_output_file = f"{output_dir}/ifc_downsampled_{int(rate*100)}percent.ply"
        
        points, colors = convert_ifc_to_pointcloud(
            ifc_file,
            element_types=None,    # Include all element types
            sampling_rate=rate,    # Use specified sampling rate
            save_to_file=downsampled_output_file,
            visualize=True         # Visualize the point cloud
        )
        
        if points is not None:
            print(f"Successfully converted IFC to {rate*100}% downsampled point cloud with {points.shape[0]} points")
            print(f"Saved point cloud to: {downsampled_output_file}")
            
            # If the point cloud is still very large, apply additional voxel downsampling
            if points.shape[0] > 100000:  # If more than 100k points
                print("Point cloud is still large, applying additional voxel downsampling...")
                
                # Create Open3D point cloud
                pcd = o3d.geometry.PointCloud()
                pcd.points = o3d.utility.Vector3dVector(points)
                if colors is not None:
                    pcd.colors = o3d.utility.Vector3dVector(colors)
                
                # Apply voxel downsampling
                voxel_size = 0.05  # Adjust based on your model size
                pcd_down = pcd.voxel_down_sample(voxel_size=voxel_size)
                
                # Save voxel downsampled point cloud
                voxel_output_file = f"{output_dir}/ifc_voxel_downsampled_{int(rate*100)}percent.ply"
                o3d.io.write_point_cloud(voxel_output_file, pcd_down)
                
                # Display results
                print(f"Applied voxel downsampling with voxel size {voxel_size}")
                print(f"Original points: {points.shape[0]}, After voxel downsampling: {len(pcd_down.points)}")
                print(f"Saved voxel downsampled point cloud to: {voxel_output_file}")
                
                # Visualize voxel downsampled point cloud
                o3d.visualization.draw_geometries([pcd_down], window_name=f"Voxel Downsampled Point Cloud ({rate*100}%)")
        else:
            print(f"Failed to convert IFC to {rate*100}% downsampled point cloud")
else:
    print("IFC support not available. Cannot convert IFC file to point cloud.")

def load_pointcloud(filename):
    """
    Loads a point cloud from a file.
    
    Parameters:
    -----------
    filename : str
        Path to the point cloud file (.ply or .pcd format)
        
    Returns:
    --------
    points : numpy.ndarray
        Array of shape (N, 3) containing XYZ coordinates
    colors : numpy.ndarray or None
        Array of shape (N, 3) containing RGB colors if available
    """
    if not O3D_SUPPORT:
        logger.error("Open3D is not available. Cannot load point cloud.")
        return None, None
    
    try:
        # Load point cloud
        pcd = o3d.io.read_point_cloud(filename)
        
        # Extract points and colors
        points = np.asarray(pcd.points)
        colors = np.asarray(pcd.colors) if pcd.has_colors() else None
        
        logger.info(f"Loaded point cloud from {filename} with {points.shape[0]} points")
        return points, colors
    except Exception as e:
        logger.error(f"Failed to load point cloud: {str(e)}")
        return None, None

def visualize_pointcloud_2d(points, colors=None, title="Point Cloud", sample_size=10000, figsize=(15, 10)):
    """
    Visualizes a point cloud using Matplotlib (2D projections).
    
    Parameters:
    -----------
    points : numpy.ndarray
        Array of shape (N, 3) containing XYZ coordinates
    colors : numpy.ndarray, optional
        Array of shape (N, 3) containing RGB colors
    title : str, optional
        Title for the visualization
    sample_size : int, optional
        Number of points to sample for visualization (to avoid overcrowding)
    figsize : tuple, optional
        Figure size
    """
    # Sample points if needed
    if points.shape[0] > sample_size:
        indices = np.random.choice(points.shape[0], sample_size, replace=False)
        sampled_points = points[indices]
        sampled_colors = colors[indices] if colors is not None else None
    else:
        sampled_points = points
        sampled_colors = colors
    
    # Create figure
    fig = plt.figure(figsize=figsize)
    
    # Create 3 subplots for different views
    ax1 = fig.add_subplot(131)
    ax2 = fig.add_subplot(132)
    ax3 = fig.add_subplot(133, projection='3d')
    
    # Set colors
    if sampled_colors is not None:
        point_colors = sampled_colors
    else:
        point_colors = 'blue'
    
    # Plot XY projection (top view)
    ax1.scatter(sampled_points[:, 0], sampled_points[:, 1], c=point_colors, s=1, alpha=0.5)
    ax1.set_title('Top View (XY)')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.grid(True, alpha=0.3)
    
    # Plot XZ projection (front view)
    ax2.scatter(sampled_points[:, 0], sampled_points[:, 2], c=point_colors, s=1, alpha=0.5)
    ax2.set_title('Front View (XZ)')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Z')
    ax2.grid(True, alpha=0.3)
    
    # Plot 3D scatter (limited points for performance)
    very_sampled = sampled_points
    if very_sampled.shape[0] > 1000:
        indices = np.random.choice(very_sampled.shape[0], 1000, replace=False)
        very_sampled = very_sampled[indices]
        very_sampled_colors = sampled_colors[indices] if sampled_colors is not None else None
    else:
        very_sampled_colors = sampled_colors
    
    ax3.scatter(
        very_sampled[:, 0], 
        very_sampled[:, 1], 
        very_sampled[:, 2], 
        c=very_sampled_colors if very_sampled_colors is not None else 'blue',
        s=5, alpha=0.7
    )
    ax3.set_title('3D View (Limited Points)')
    ax3.set_xlabel('X')
    ax3.set_ylabel('Y')
    ax3.set_zlabel('Z')
    
    # Set main title
    plt.suptitle(f"{title} - {points.shape[0]} points", fontsize=16)
    plt.tight_layout()
    plt.show()

def export_pointcloud_to_html(points, colors=None, title="Point Cloud", sample_size=10000, output_file="pointcloud_viewer.html"):
    """
    Exports a point cloud to an HTML file with interactive 3D visualization.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Array of shape (N, 3) containing XYZ coordinates
    colors : numpy.ndarray, optional
        Array of shape (N, 3) containing RGB colors
    title : str, optional
        Title for the visualization
    sample_size : int, optional
        Number of points to sample for visualization (to avoid browser lag)
    output_file : str, optional
        Path to save the HTML file
    """
    try:
        import plotly.graph_objects as go
    except ImportError:
        print("Plotly is not installed. Installing now...")
        !pip install plotly
        import plotly.graph_objects as go
    
    # Sample points if needed
    if points.shape[0] > sample_size:
        indices = np.random.choice(points.shape[0], sample_size, replace=False)
        sampled_points = points[indices]
        sampled_colors = colors[indices] if colors is not None else None
    else:
        sampled_points = points
        sampled_colors = colors
    
    # Prepare colors
    if sampled_colors is not None:
        # Convert RGB [0-1] to RGB [0-255]
        color_str = [f'rgb({int(r*255)},{int(g*255)},{int(b*255)})' 
                    for r, g, b in sampled_colors]
    else:
        color_str = 'blue'
    
    # Create 3D scatter plot
    fig = go.Figure(data=[go.Scatter3d(
        x=sampled_points[:, 0],
        y=sampled_points[:, 1],
        z=sampled_points[:, 2],
        mode='markers',
        marker=dict(
            size=2,
            color=color_str,
            opacity=0.7
        )
    )])
    
    # Update layout
    fig.update_layout(
        title=f"{title} - {points.shape[0]} points (showing {sampled_points.shape[0]} sampled points)",
        scene=dict(
            xaxis_title='X',
            yaxis_title='Y',
            zaxis_title='Z',
            aspectmode='data'
        ),
        width=900,
        height=700,
    )
    
    # Write to HTML file
    fig.write_html(output_file)
    print(f"Exported interactive 3D visualization to {output_file}")
    
    # Download the HTML file
    from google.colab import files
    files.download(output_file)

# Load and visualize a saved point cloud with Matplotlib
print("\n=== Loading and Visualizing Saved Point Cloud with Matplotlib ===\n")

# Check if the file exists
if os.path.exists(output_file):
    points, colors = load_pointcloud(output_file)
    
    if points is not None:
        print(f"Loaded point cloud with {points.shape[0]} points")
        
        # Visualize with Matplotlib
        visualize_pointcloud_2d(points, colors, "IFC Point Cloud", sample_size=50000)
        
        # Also visualize the structural elements point cloud if it exists
        structural_file = f"{output_dir}/ifc_structural_elements.ply"
        if os.path.exists(structural_file):
            struct_points, struct_colors = load_pointcloud(structural_file)
            if struct_points is not None:
                print(f"Loaded structural elements point cloud with {struct_points.shape[0]} points")
                visualize_pointcloud_2d(struct_points, struct_colors, "Structural Elements", sample_size=20000)
        
        # Also visualize the top elements point cloud if it exists
        top_file = f"{output_dir}/ifc_top_elements.ply"
        if os.path.exists(top_file):
            top_points, top_colors = load_pointcloud(top_file)
            if top_points is not None:
                print(f"Loaded top elements point cloud with {top_points.shape[0]} points")
                visualize_pointcloud_2d(top_points, top_colors, "Top Elements", sample_size=20000)
    else:
        print("Failed to load point cloud")
else:
    print(f"Point cloud file not found: {output_file}")

# Export point clouds to HTML for offline viewing
print("\n=== Exporting Point Clouds to HTML for Offline Viewing ===\n")

# Check if the file exists
if os.path.exists(output_file):
    points, colors = load_pointcloud(output_file)
    
    if points is not None:
        print(f"Loaded point cloud with {points.shape[0]} points")
        
        # Export to HTML
        export_pointcloud_to_html(
            points, colors, 
            title="IFC Point Cloud", 
            sample_size=20000, 
            output_file="ifc_pointcloud_viewer.html"
        )
        
        # Also export the structural elements point cloud if it exists
        structural_file = f"{output_dir}/ifc_structural_elements.ply"
        if os.path.exists(structural_file):
            struct_points, struct_colors = load_pointcloud(structural_file)
            if struct_points is not None:
                print(f"Loaded structural elements point cloud with {struct_points.shape[0]} points")
                export_pointcloud_to_html(
                    struct_points, struct_colors, 
                    title="Structural Elements", 
                    sample_size=10000, 
                    output_file="ifc_structural_elements_viewer.html"
                )
        
        # Also export the top elements point cloud if it exists
        top_file = f"{output_dir}/ifc_top_elements.ply"
        if os.path.exists(top_file):
            top_points, top_colors = load_pointcloud(top_file)
            if top_points is not None:
                print(f"Loaded top elements point cloud with {top_points.shape[0]} points")
                export_pointcloud_to_html(
                    top_points, top_colors, 
                    title="Top Elements", 
                    sample_size=10000, 
                    output_file="ifc_top_elements_viewer.html"
                )
    else:
        print("Failed to load point cloud")
else:
    print(f"Point cloud file not found: {output_file}")

# Load and visualize a saved point cloud with Open3D (may not work in Colab)
if O3D_SUPPORT:
    print("\n=== Loading and Visualizing Saved Point Cloud with Open3D ===\n")
    print("Note: This may not work in Colab's headless environment.")
    
    # Check if the file exists
    if os.path.exists(output_file):
        points, colors = load_pointcloud(output_file)
        
        if points is not None:
            print(f"Loaded point cloud with {points.shape[0]} points")
            visualize_pointcloud(points, colors, "Loaded Point Cloud")
        else:
            print("Failed to load point cloud")
    else:
        print(f"Point cloud file not found: {output_file}")
else:
    print("Open3D support not available. Cannot load and visualize point cloud.")

# %% [markdown]
# # IFC to Point Cloud Conversion
#
# This notebook converts an IFC file to a point cloud for use in alignment tasks with drone-generated point clouds. It extracts geometric vertices from the IFC model, handling various representation types and adding robustness for missing data.
#
# **Stage**: Preprocessing  
# **Input Data**: IFC file  
# **Output**: Point cloud in PLY and XYZ formats  
# **Method**: IfcOpenShell for geometry extraction  
#
# **Author**: Preetam Balijepalli  
# **Date**: July 02, 2025  
# **Project**: As-Built Foundation Analysis

# %% [markdown]
# ## 1. Environment Setup
#
# Configure the environment with required libraries for IFC processing.

# %%
# Install required package
!pip install ifcopenshell open3d

# %%
# Import libraries
import numpy as np
import os
import logging
from pathlib import Path
from datetime import datetime
import ifcopenshell
import open3d as o3d

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("Environment Initialized")
print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# %% [markdown]
# ## 2. Configuration Parameters
#
# Define parameters for IFC conversion and output paths.

# %%
# Configuration parameters
ifc_file = "/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc"
output_dir = "../../data/output_runs/foundation_analysis/castro_area4/preprocessing"
output_ply = "ifc_pointcloud.ply"
output_xyz = "ifc_pointcloud.xyz"

# Setup paths
base_path = Path('../..')
data_path = base_path / 'data'
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)
full_ply_path = output_path / output_ply
full_xyz_path = output_path / output_xyz

print(f"IFC file: {ifc_file}")
print(f"Output directory: {output_path}")
print(f"Output PLY: {full_ply_path}")
print(f"Output XYZ: {full_xyz_path}")

# %% [markdown]
# ## 3. IFC to Point Cloud Conversion
#
# Convert the IFC file to a point cloud by extracting vertices from building elements, with enhanced representation handling.

# %%
def convert_ifc_to_pointcloud(ifc_path, output_ply_path, output_xyz_path):
    """Converts an IFC file to a point cloud and saves in PLY and XYZ formats."""
    logger.info(f"Converting IFC file: {ifc_path}")
    try:
        ifc = ifcopenshell.open(ifc_path)
        points = []
        for element in ifc.by_type("IfcBuildingElement"):
            if hasattr(element, "Representation") and element.Representation is not None:
                for rep in element.Representation.Representations:
                    if rep is not None:
                        for item in rep.Items:
                            if item is not None:
                                # Handle different representation types
                                if item.is_a("IfcFacetedBrep"):
                                    for face in item.Outer.CfsFaces:
                                        for loop in face.Bounds:
                                            for vertex in loop.Bound.Polygon:
                                                points.append(vertex.Coordinates)
                                elif item.is_a("IfcExtrudedAreaSolid"):
                                    if hasattr(item, "SweptArea") and item.SweptArea.is_a("IfcArbitraryClosedProfileDef"):
                                        for point in item.SweptArea.OuterCurve.Points:
                                            points.append([point.X, point.Y, 0.0])  # Z=0, adjust if needed
                                elif item.is_a("IfcTessellatedFaceSet"):
                                    for coord in item.Coordinates.CoordList:
                                        points.append(coord)
                                elif item.is_a("IfcTriangulatedFaceSet"):
                                    for coord in item.Coordinates.CoordList:
                                        points.append(coord)
                                elif item.is_a("IfcMappedItem"):
                                    # Handle mapped geometry by accessing the mapping source
                                    if hasattr(item, "MappingSource") and item.MappingSource.MappedRepresentation:
                                        for mapped_item in item.MappingSource.MappedRepresentation.Items:
                                            if mapped_item.is_a("IfcTessellatedFaceSet"):
                                                for coord in mapped_item.Coordinates.CoordList:
                                                    points.append(coord)

        if not points:
            logger.warning("No points extracted from IfcBuildingElement. Checking all products for tessellation.")
            for element in ifc.by_type("IfcProduct"):
                if hasattr(element, "Representation") and element.Representation is not None:
                    for rep in element.Representation.Representations:
                        if rep is not None and rep.RepresentationType == "Tessellation":
                            for item in rep.Items:
                                if item is not None and item.is_a("IfcTessellatedFaceSet"):
                                    for coord in item.Coordinates.CoordList:
                                        points.append(coord)
                                elif item.is_a("IfcTriangulatedFaceSet"):
                                    for coord in item.Coordinates.CoordList:
                                        points.append(coord)

        points = np.array(points) if points else np.array([])
        logger.info(f"Extracted {points.shape[0]} points from IFC")

        if points.size == 0:
            raise ValueError("No valid geometric data found in IFC file. Check representation types or file integrity.")

        # Save as XYZ
        np.savetxt(str(output_xyz_path), points, fmt="%f %f %f", delimiter=" ")
        logger.info(f"Saved point cloud to {output_xyz_path}")

        # Convert to Open3D point cloud and save as PLY
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        o3d.io.write_point_cloud(str(output_ply_path), pcd)
        logger.info(f"Saved point cloud to {output_ply_path}")
        return points
    except Exception as e:
        logger.error(f"Error converting IFC to point cloud: {e}")
        return None

# Perform conversion
source_points = convert_ifc_to_pointcloud(ifc_file, full_ply_path, full_xyz_path)
if source_points is None:
    raise ValueError("Failed to convert IFC to point cloud")

print(f"Conversion completed. Total points: {source_points.shape[0]}")