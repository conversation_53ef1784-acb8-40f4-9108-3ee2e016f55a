{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC to Point Cloud Converter\n", "\n", "This notebook provides tools to convert IFC (Industry Foundation Classes) files to point clouds. It allows you to:\n", "\n", "1. Load IFC files and extract geometry\n", "2. Convert IFC elements to point clouds\n", "3. Visualize the resulting point clouds\n", "4. Save point clouds to various formats (PLY, PCD)\n", "5. Filter IFC elements by type\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** June 2024"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Installation\n", "\n", "First, let's install the necessary dependencies and import required libraries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "# Uncomment and run this cell if you need to install the packages\n", "\n", "# !pip install ifcopenshell open3d numpy matplotlib"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import logging\n", "import ifcopenshell\n", "import ifcopenshell.geom\n", "import open3d as o3d\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, \n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "# Mount Google Drive if in Colab\n", "try:\n", "    from google.colab import drive\n", "    drive.mount('/content/gdrive')\n", "    IN_COLAB = True\n", "    logger.info(\"Google Drive mounted successfully.\")\n", "except ImportError:\n", "    IN_COLAB = False\n", "    logger.info(\"Not running in Google Colab. Using local file system.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. IFC to Point Cloud Conversion Functions\n", "\n", "Now let's define the functions to convert IFC files to point clouds."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_ifc_to_pointcloud(filename, element_types=None, sampling_rate=1.0, save_to_file=None, visualize=False):\n", "    \"\"\"\n", "    Converts an IFC file to a point cloud with enhanced control options.\n", "\n", "    \"\"\"\n", "    logger.info(f\"Converting IFC file to point cloud: {filename}\")\n", "    \n", "    if not IFC_SUPPORT:\n", "        logger.error(\"IFC support is not available. Please install ifcopenshell.\")\n", "        return None, None\n", "    \n", "    try:\n", "        # Load IFC file\n", "        ifc_file = ifcopenshell.open(filename)\n", "        \n", "        # Set up geometry settings\n", "        settings = ifcopenshell.geom.settings()\n", "        settings.set(settings.USE_WORLD_COORDS, True)\n", "        \n", "        # Get all products or filter by element types\n", "        if element_types:\n", "            products = []\n", "            for element_type in element_types:\n", "                products.extend(ifc_file.by_type(element_type))\n", "            logger.info(f\"Found {len(products)} products of types {element_types}\")\n", "        else:\n", "            products = ifc_file.by_type(\"IfcProduct\")\n", "            logger.info(f\"Found {len(products)} products in IFC file\")\n", "        \n", "        # Extract all vertices from all shapes\n", "        all_vertices = []\n", "        product_colors = []  # For visualization\n", "        \n", "        # Process products\n", "        for product in products:\n", "            if product.Representation:\n", "                try:\n", "                    # Create a shape from the product\n", "                    shape = ifcopenshell.geom.create_shape(settings, product)\n", "                    \n", "                    # Get the vertices\n", "                    verts = shape.geometry.verts\n", "                    if verts:\n", "                        # Reshape to (N, 3) array\n", "                        points = np.array(verts).reshape(-1, 3)\n", "                        \n", "                        # Apply sampling if needed\n", "                        if sampling_rate < 1.0:\n", "                            num_points = max(1, int(points.shape[0] * sampling_rate))\n", "                            indices = np.random.choice(points.shape[0], num_points, replace=False)\n", "                            points = points[indices]\n", "                        \n", "                        all_vertices.append(points)\n", "                        \n", "                        # Get color for visualization\n", "                        try:\n", "                            color = shape.geometry.colors\n", "                            if color:\n", "                                color = np.array(color).reshape(-1, 4)[:, :3]  # RGBA to RGB\n", "                                product_colors.append(np.tile(color[0], (points.shape[0], 1)))\n", "                            else:\n", "                                # Generate a random color for this product\n", "                                random_color = np.random.rand(3)\n", "                                product_colors.append(np.tile(random_color, (points.shape[0], 1)))\n", "                        except:\n", "                            # Generate a random color for this product\n", "                            random_color = np.random.rand(3)\n", "                            product_colors.append(np.tile(random_color, (points.shape[0], 1)))\n", "                except Exception as e:\n", "                    # Skip products that can't be processed\n", "                    logger.warning(f\"Skipping product {product.id()}: {str(e)}\")\n", "                    continue\n", "        \n", "        if not all_vertices:\n", "            logger.error(f\"No geometry found in IFC file: {filename}\")\n", "            return None, None\n", "            \n", "        # Combine all vertices into a single array\n", "        combined_vertices = np.vstack(all_vertices)\n", "        combined_colors = np.vstack(product_colors) if product_colors else None\n", "        \n", "        logger.info(f\"Extracted {combined_vertices.shape[0]} points from {filename}\")\n", "        \n", "        # Visualize if requested\n", "        if visualize and O3D_SUPPORT and combined_vertices.shape[0] > 0:\n", "            visualize_pointcloud(combined_vertices, combined_colors, \"IFC Point Cloud\")\n", "        \n", "        # Save to file if requested\n", "        if save_to_file and O3D_SUPPORT and combined_vertices.shape[0] > 0:\n", "            save_pointcloud(combined_vertices, save_to_file, combined_colors)\n", "        \n", "        return combined_vertices, combined_colors\n", "    except Exception as e:\n", "        logger.error(f\"Error converting IFC file to point cloud: {e}\")\n", "        return None, None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_pointcloud(points, colors=None, title=\"Point Cloud\"):\n", "    \"\"\"\n", "    Visualizes a point cloud using Open3D.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Array of shape (N, 3) containing XYZ coordinates\n", "    colors : numpy.ndarray, optional\n", "        Array of shape (N, 3) containing RGB colors\n", "    title : str, optional\n", "        Title for the visualization window\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        logger.error(\"Open3D is not available. Cannot visualize point cloud.\")\n", "        return\n", "    \n", "    try:\n", "        # Create Open3D point cloud\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        \n", "        # Add colors if available\n", "        if colors is not None:\n", "            pcd.colors = o3d.utility.Vector3dVector(colors)\n", "        \n", "        # Visualize\n", "        logger.info(f\"Visualizing point cloud with {points.shape[0]} points...\")\n", "        o3d.visualization.draw_geometries([pcd], window_name=title)\n", "    except Exception as e:\n", "        logger.error(f\"Visualization failed: {str(e)}\")\n", "\n", "def save_pointcloud(points, filename, colors=None):\n", "    \"\"\"\n", "    Saves a point cloud to a file.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Array of shape (N, 3) containing XYZ coordinates\n", "    colors : numpy.ndarray, optional\n", "        Array of shape (N, 3) containing RGB colors\n", "    filename : str\n", "        Path to save the point cloud (.ply or .pcd format)\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        logger.error(\"Open3D is not available. Cannot save point cloud.\")\n", "        return\n", "    \n", "    try:\n", "        # Create Open3D point cloud\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        \n", "        # Add colors if available\n", "        if colors is not None:\n", "            pcd.colors = o3d.utility.Vector3dVector(colors)\n", "        \n", "        # Save to file\n", "        if filename.lower().endswith('.ply'):\n", "            o3d.io.write_point_cloud(filename, pcd)\n", "        elif filename.lower().endswith('.pcd'):\n", "            o3d.io.write_point_cloud(filename, pcd)\n", "        else:\n", "            # Default to PLY if no extension or unknown extension\n", "            filename = filename + '.ply' if '.' not in filename else filename\n", "            o3d.io.write_point_cloud(filename, pcd)\n", "        \n", "        logger.info(f\"Saved point cloud to {filename}\")\n", "    except Exception as e:\n", "        logger.error(f\"Failed to save point cloud: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. List Available IFC Element Types\n", "\n", "This function helps you explore what element types are available in your IFC file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def list_ifc_element_types(filename):\n", "    \"\"\"\n", "    Lists all element types in an IFC file and their counts.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the IFC file\n", "        \n", "    Returns:\n", "    --------\n", "    element_types : dict\n", "        Dictionary with element types as keys and counts as values\n", "    \"\"\"\n", "    if not IFC_SUPPORT:\n", "        logger.error(\"IFC support is not available. Please install ifcopenshell.\")\n", "        return None\n", "    \n", "    try:\n", "        # Load IFC file\n", "        ifc_file = ifcopenshell.open(filename)\n", "        \n", "        # Get all entity types\n", "        entity_types = {}\n", "        for entity in ifc_file.by_type(\"IfcProduct\"):\n", "            entity_type = entity.is_a()\n", "            if entity_type in entity_types:\n", "                entity_types[entity_type] += 1\n", "            else:\n", "                entity_types[entity_type] = 1\n", "        \n", "        # Sort by count (descending)\n", "        sorted_types = dict(sorted(entity_types.items(), key=lambda item: item[1], reverse=True))\n", "        \n", "        # Print summary\n", "        print(f\"\\nIFC Element Types in {os.path.basename(filename)}:\")\n", "        print(\"-\" * 50)\n", "        print(f\"{'Element Type':<30} {'Count':>10}\")\n", "        print(\"-\" * 50)\n", "        for entity_type, count in sorted_types.items():\n", "            print(f\"{entity_type:<30} {count:>10}\")\n", "        print(\"-\" * 50)\n", "        print(f\"{'Total':<30} {sum(sorted_types.values()):>10}\")\n", "        \n", "        return sorted_types\n", "    except Exception as e:\n", "        logger.error(f\"Error listing IFC element types: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Convert IFC to Point Cloud\n", "\n", "Now let's use our functions to convert an IFC file to a point cloud."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the path to your IFC file\n", "if IN_COLAB:\n", "    # Google Drive path - using the specific path provided\n", "    base_path = '/content/gdrive/MyDrive/pc-experiment'\n", "else:\n", "    # Local path - adjust as needed\n", "    base_path = 'data/pc-experiment'\n", "\n", "# IFC file path - using the specific file provided\n", "ifc_file = f\"{base_path}/GRE.EEC.S.00.IT.P.14353.00.265.ifc\"\n", "\n", "# Create output directory if it doesn't exist\n", "output_dir = f\"{base_path}/output\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Output file path for the point cloud\n", "output_file = f\"{output_dir}/ifc_pointcloud.ply\"\n", "\n", "print(f\"IFC file: {ifc_file}\")\n", "print(f\"Output file: {output_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def check_ifc_compatibility(filename):\n", "    \"\"\"\n", "    Checks if an IFC file is compatible with our conversion process.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the IFC file\n", "        \n", "    Returns:\n", "    --------\n", "    compatibility : dict\n", "        Dictionary with compatibility information\n", "    \"\"\"\n", "    if not IFC_SUPPORT:\n", "        return {\"compatible\": False, \"reason\": \"ifcopenshell not installed\"}\n", "    \n", "    try:\n", "        # Try to open the IFC file\n", "        ifc_file = ifcopenshell.open(filename)\n", "        \n", "        # Get schema information\n", "        schema = ifc_file.schema\n", "        \n", "        # Count entities\n", "        product_count = len(ifc_file.by_type(\"IfcProduct\"))\n", "        \n", "        # Check for common entity types\n", "        common_types = [\"IfcWall\", \"IfcSlab\", \"IfcColumn\", \"IfcBeam\", \"IfcDoor\", \"IfcWindow\"]\n", "        found_types = []\n", "        for entity_type in common_types:\n", "            if len(ifc_file.by_type(entity_type)) > 0:\n", "                found_types.append(entity_type)\n", "        \n", "        # Check file size\n", "        file_size_mb = os.path.getsize(filename) / (1024 * 1024)\n", "        \n", "        # Determine compatibility\n", "        is_compatible = True\n", "        warnings = []\n", "        \n", "        if product_count == 0:\n", "            is_compatible = False\n", "            warnings.append(\"No IfcProduct entities found\")\n", "        \n", "        if file_size_mb > 100:\n", "            warnings.append(f\"Large file size ({file_size_mb:.1f} MB) may cause memory issues\")\n", "        \n", "        if not found_types:\n", "            warnings.append(\"No common building elements found\")\n", "        \n", "        # Return compatibility information\n", "        return {\n", "            \"compatible\": is_compatible,\n", "            \"schema\": schema,\n", "            \"product_count\": product_count,\n", "            \"found_types\": found_types,\n", "            \"file_size_mb\": file_size_mb,\n", "            \"warnings\": warnings\n", "        }\n", "    except Exception as e:\n", "        return {\"compatible\": False, \"reason\": str(e)}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check IFC file compatibility\n", "if IFC_SUPPORT:\n", "    print(\"\\n=== Checking IFC File Compatibility ===\\n\")\n", "    compatibility = check_ifc_compatibility(ifc_file)\n", "    \n", "    if compatibility[\"compatible\"]:\n", "        print(f\"✅ IFC file is compatible\")\n", "        print(f\"Schema: {compatibility['schema']}\")\n", "        print(f\"Product Count: {compatibility['product_count']}\")\n", "        print(f\"File Size: {compatibility['file_size_mb']:.1f} MB\")\n", "        print(f\"Found Types: {', '.join(compatibility['found_types'])}\")\n", "        \n", "        if compatibility['warnings']:\n", "            print(\"\\nWarnings:\")\n", "            for warning in compatibility['warnings']:\n", "                print(f\"⚠️ {warning}\")\n", "    else:\n", "        print(f\"❌ IFC file is not compatible: {compatibility.get('reason', 'Unknown reason')}\")\n", "        print(\"Please try a different IFC file.\")\n", "else:\n", "    print(\"IFC support not available. Cannot check compatibility.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# List all element types in the IFC file\n", "if IFC_SUPPORT:\n", "    element_types = list_ifc_element_types(ifc_file)\n", "else:\n", "    print(\"IFC support not available. Cannot list element types.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced conversion function with better error handling\n", "def convert_ifc_to_pointcloud_safe(filename, element_types=None, sampling_rate=1.0, save_to_file=None, visualize=False, max_points=10000000):\n", "    \"\"\"\n", "    Enhanced version of convert_ifc_to_pointcloud with better error handling and progress tracking.\n", "    \n", "    Parameters are the same as convert_ifc_to_pointcloud with the addition of:\n", "    max_points : int, optional\n", "        Maximum number of points to extract (to prevent memory issues)\n", "    \"\"\"\n", "    logger.info(f\"Converting IFC file to point cloud (safe mode): {filename}\")\n", "    \n", "    if not IFC_SUPPORT:\n", "        logger.error(\"IFC support is not available. Please install ifcopenshell.\")\n", "        return None, None\n", "    \n", "    # Check compatibility first\n", "    compatibility = check_ifc_compatibility(filename)\n", "    if not compatibility[\"compatible\"]:\n", "        logger.error(f\"IFC file is not compatible: {compatibility.get('reason', 'Unknown reason')}\")\n", "        return None, None\n", "    \n", "    # Log compatibility information\n", "    logger.info(f\"IFC Schema: {compatibility['schema']}\")\n", "    logger.info(f\"Product Count: {compatibility['product_count']}\")\n", "    logger.info(f\"File Size: {compatibility['file_size_mb']:.1f} MB\")\n", "    \n", "    # Log any warnings\n", "    for warning in compatibility.get(\"warnings\", []):\n", "        logger.warning(f\"Compatibility warning: {warning}\")\n", "    \n", "    try:\n", "        # Load IFC file\n", "        ifc_file = ifcopenshell.open(filename)\n", "        \n", "        # Set up geometry settings\n", "        settings = ifcopenshell.geom.settings()\n", "        settings.set(settings.USE_WORLD_COORDS, True)\n", "        \n", "        # Get all products or filter by element types\n", "        if element_types:\n", "            products = []\n", "            for element_type in element_types:\n", "                products.extend(ifc_file.by_type(element_type))\n", "            logger.info(f\"Found {len(products)} products of types {element_types}\")\n", "        else:\n", "            products = ifc_file.by_type(\"IfcProduct\")\n", "            logger.info(f\"Found {len(products)} products in IFC file\")\n", "        \n", "        # Extract all vertices from all shapes\n", "        all_vertices = []\n", "        product_colors = []  # For visualization\n", "        \n", "        # Track progress and errors\n", "        processed_count = 0\n", "        error_count = 0\n", "        total_points = 0\n", "        \n", "        # Process products with progress tracking\n", "        print(f\"Processing {len(products)} products...\")\n", "        for i, product in enumerate(products):\n", "            # Print progress every 100 products or 10% of total\n", "            progress_interval = max(1, min(100, len(products) // 10))\n", "            if i % progress_interval == 0 and i > 0:\n", "                print(f\"Progress: {i}/{len(products)} products ({i/len(products)*100:.1f}%), {total_points} points\")\n", "            \n", "            # Check if we've exceeded the maximum point count\n", "            if total_points > max_points:\n", "                logger.warning(f\"Reached maximum point count ({max_points}). Stopping extraction.\")\n", "                break\n", "                \n", "            if product.Representation:\n", "                try:\n", "                    # Create a shape from the product\n", "                    shape = ifcopenshell.geom.create_shape(settings, product)\n", "                    \n", "                    # Get the vertices\n", "                    verts = shape.geometry.verts\n", "                    if verts:\n", "                        # Reshape to (N, 3) array\n", "                        points = np.array(verts).reshape(-1, 3)\n", "                        \n", "                        # Apply sampling if needed\n", "                        if sampling_rate < 1.0:\n", "                            num_points = max(1, int(points.shape[0] * sampling_rate))\n", "                            indices = np.random.choice(points.shape[0], num_points, replace=False)\n", "                            points = points[indices]\n", "                        \n", "                        all_vertices.append(points)\n", "                        total_points += points.shape[0]\n", "                        \n", "                        # Get color for visualization\n", "                        try:\n", "                            color = shape.geometry.colors\n", "                            if color:\n", "                                color = np.array(color).reshape(-1, 4)[:, :3]  # RGBA to RGB\n", "                                product_colors.append(np.tile(color[0], (points.shape[0], 1)))\n", "                            else:\n", "                                # Generate a random color for this product\n", "                                random_color = np.random.rand(3)\n", "                                product_colors.append(np.tile(random_color, (points.shape[0], 1)))\n", "                        except:\n", "                            # Generate a random color for this product\n", "                            random_color = np.random.rand(3)\n", "                            product_colors.append(np.tile(random_color, (points.shape[0], 1)))\n", "                        \n", "                        processed_count += 1\n", "                except Exception as e:\n", "                    # Skip products that can't be processed\n", "                    error_count += 1\n", "                    if error_count < 10:  # Only log the first few errors to avoid spam\n", "                        logger.warning(f\"Skipping product {product.id()}: {str(e)}\")\n", "                    elif error_count == 10:\n", "                        logger.warning(\"Too many errors, suppressing further error messages...\")\n", "                    continue\n", "        \n", "        # Log processing summary\n", "        print(f\"Processing complete: {processed_count} products processed, {error_count} errors\")\n", "        \n", "        if not all_vertices:\n", "            logger.error(f\"No geometry found in IFC file: {filename}\")\n", "            return None, None\n", "            \n", "        # Combine all vertices into a single array\n", "        combined_vertices = np.vstack(all_vertices)\n", "        combined_colors = np.vstack(product_colors) if product_colors else None\n", "        \n", "        logger.info(f\"Extracted {combined_vertices.shape[0]} points from {filename}\")\n", "        \n", "        # Check for NaN or Inf values\n", "        if np.isnan(combined_vertices).any() or np.isinf(combined_vertices).any():\n", "            logger.warning(\"Found NaN or Inf values in point cloud. Cleaning...\")\n", "            valid_mask = ~(np.isnan(combined_vertices).any(axis=1) | np.isinf(combined_vertices).any(axis=1))\n", "            combined_vertices = combined_vertices[valid_mask]\n", "            if combined_colors is not None:\n", "                combined_colors = combined_colors[valid_mask]\n", "            logger.info(f\"After cleaning: {combined_vertices.shape[0]} points\")\n", "        \n", "        # Visualize if requested\n", "        if visualize and O3D_SUPPORT and combined_vertices.shape[0] > 0:\n", "            visualize_pointcloud(combined_vertices, combined_colors, \"IFC Point Cloud\")\n", "        \n", "        # Save to file if requested\n", "        if save_to_file and O3D_SUPPORT and combined_vertices.shape[0] > 0:\n", "            save_pointcloud(combined_vertices, save_to_file, combined_colors)\n", "        \n", "        return combined_vertices, combined_colors\n", "    except MemoryError:\n", "        logger.error(f\"Memory error while processing IFC file. Try reducing sampling_rate or max_points.\")\n", "        return None, None\n", "    except Exception as e:\n", "        logger.error(f\"Error converting IFC file to point cloud: {e}\")\n", "        return None, None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert IFC to point cloud with all elements using the enhanced safe conversion\n", "if IFC_SUPPORT:\n", "    print(\"\\n=== Converting IFC to Point Cloud (All Elements) ===\\n\")\n", "    \n", "    # First check if the file is very large\n", "    compatibility = check_ifc_compatibility(ifc_file)\n", "    file_size_mb = compatibility.get('file_size_mb', 0)\n", "    \n", "    # Adjust sampling rate based on file size\n", "    if file_size_mb > 200:  # Very large file\n", "        sampling_rate = 0.1  # Use 10% of points\n", "        print(f\"File is very large ({file_size_mb:.1f} MB), using 10% sampling rate\")\n", "    elif file_size_mb > 50:  # Medium-large file\n", "        sampling_rate = 0.5  # Use 50% of points\n", "        print(f\"File is large ({file_size_mb:.1f} MB), using 50% sampling rate\")\n", "    else:  # Small file\n", "        sampling_rate = 1.0  # Use all points\n", "        print(f\"File is small ({file_size_mb:.1f} MB), using 100% sampling rate\")\n", "    \n", "    # Convert with safe mode\n", "    points, colors = convert_ifc_to_pointcloud_safe(\n", "        ifc_file,\n", "        element_types=None,  # Include all element types\n", "        sampling_rate=sampling_rate,\n", "        save_to_file=output_file,\n", "        visualize=True,      # Visualize the point cloud\n", "        max_points=5000000   # Limit to 5 million points to prevent memory issues\n", "    )\n", "    \n", "    if points is not None:\n", "        print(f\"Successfully converted IFC to point cloud with {points.shape[0]} points\")\n", "        print(f\"Saved point cloud to: {output_file}\")\n", "        \n", "        # If we have a lot of points, also create a downsampled version\n", "        if points.shape[0] > 100000:  # More than 100k points\n", "            print(\"\\nCreating downsampled version for easier handling...\")\n", "            \n", "            # Create Open3D point cloud\n", "            pcd = o3d.geometry.PointCloud()\n", "            pcd.points = o3d.utility.Vector3dVector(points)\n", "            if colors is not None:\n", "                pcd.colors = o3d.utility.Vector3dVector(colors)\n", "            \n", "            # Apply voxel downsampling\n", "            voxel_size = 0.05  # Adjust based on your model size\n", "            pcd_down = pcd.voxel_down_sample(voxel_size=voxel_size)\n", "            \n", "            # Save voxel downsampled point cloud\n", "            voxel_output_file = output_file.replace('.ply', '_voxel_downsampled.ply')\n", "            o3d.io.write_point_cloud(voxel_output_file, pcd_down)\n", "            \n", "            # Display results\n", "            print(f\"Applied voxel downsampling with voxel size {voxel_size}\")\n", "            print(f\"Original points: {points.shape[0]}, After voxel downsampling: {len(pcd_down.points)}\")\n", "            print(f\"Saved voxel downsampled point cloud to: {voxel_output_file}\")\n", "    else:\n", "        print(\"Failed to convert IFC to point cloud\")\n", "else:\n", "    print(\"IFC support not available. Cannot convert IFC file to point cloud.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Convert Specific IFC Elements to Point Cloud\n", "\n", "Now let's convert only specific element types (e.g., walls, slabs) to a point cloud."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert specific IFC elements to point cloud\n", "if IFC_SUPPORT:\n", "    # First, list all element types in the IFC file to see what's available\n", "    print(\"\\n=== Analyzing IFC File Element Types ===\\n\")\n", "    element_types_dict = list_ifc_element_types(ifc_file)\n", "    \n", "    # Select specific element types based on what's in the file\n", "    # We'll create multiple point clouds with different element types\n", "    \n", "    # 1. Structural elements\n", "    structural_types = [\"IfcWall\", \"IfcSlab\", \"IfcColumn\", \"IfcBeam\", \"IfcFooting\", \"IfcPile\", \"IfcRoof\"]\n", "    # Filter to only include types that exist in the file\n", "    structural_types = [t for t in structural_types if t in element_types_dict]\n", "    \n", "    if structural_types:\n", "        print(f\"\\n=== Converting Structural Elements to Point Cloud: {structural_types} ===\\n\")\n", "        \n", "        # Output file for structural elements\n", "        structural_output_file = f\"{output_dir}/ifc_structural_elements.ply\"\n", "        \n", "        points, colors = convert_ifc_to_pointcloud(\n", "            ifc_file,\n", "            element_types=structural_types,\n", "            sampling_rate=1.0,\n", "            save_to_file=structural_output_file,\n", "            visualize=True\n", "        )\n", "        \n", "        if points is not None:\n", "            print(f\"Successfully converted structural elements to point cloud with {points.shape[0]} points\")\n", "            print(f\"Saved point cloud to: {structural_output_file}\")\n", "        else:\n", "            print(\"Failed to convert structural elements to point cloud\")\n", "    else:\n", "        print(\"No structural elements found in the IFC file\")\n", "    \n", "    # 2. MEP elements (Mechanical, Electrical, Plumbing)\n", "    mep_types = [\"IfcPipe\", \"IfcDuctSegment\", \"IfcCableSegment\", \"IfcFlowTerminal\", \"IfcFlowController\", \"IfcFlowFitting\"]\n", "    # Filter to only include types that exist in the file\n", "    mep_types = [t for t in mep_types if t in element_types_dict]\n", "    \n", "    if mep_types:\n", "        print(f\"\\n=== Converting MEP Elements to Point Cloud: {mep_types} ===\\n\")\n", "        \n", "        # Output file for MEP elements\n", "        mep_output_file = f\"{output_dir}/ifc_mep_elements.ply\"\n", "        \n", "        points, colors = convert_ifc_to_pointcloud(\n", "            ifc_file,\n", "            element_types=mep_types,\n", "            sampling_rate=1.0,\n", "            save_to_file=mep_output_file,\n", "            visualize=True\n", "        )\n", "        \n", "        if points is not None:\n", "            print(f\"Successfully converted MEP elements to point cloud with {points.shape[0]} points\")\n", "            print(f\"Saved point cloud to: {mep_output_file}\")\n", "        else:\n", "            print(\"Failed to convert MEP elements to point cloud\")\n", "    else:\n", "        print(\"No MEP elements found in the IFC file\")\n", "    \n", "    # 3. Custom selection - choose the top 3 most numerous element types\n", "    top_types = list(element_types_dict.keys())[:3]\n", "    \n", "    if top_types:\n", "        print(f\"\\n=== Converting Top 3 Most Common Element Types to Point Cloud: {top_types} ===\\n\")\n", "        \n", "        # Output file for top elements\n", "        top_output_file = f\"{output_dir}/ifc_top_elements.ply\"\n", "        \n", "        points, colors = convert_ifc_to_pointcloud(\n", "            ifc_file,\n", "            element_types=top_types,\n", "            sampling_rate=1.0,\n", "            save_to_file=top_output_file,\n", "            visualize=True\n", "        )\n", "        \n", "        if points is not None:\n", "            print(f\"Successfully converted top element types to point cloud with {points.shape[0]} points\")\n", "            print(f\"Saved point cloud to: {top_output_file}\")\n", "        else:\n", "            print(\"Failed to convert top element types to point cloud\")\n", "else:\n", "    print(\"IFC support not available. Cannot convert IFC file to point cloud.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON> Large Point Clouds\n", "\n", "For very large IFC files, you can downsample the point cloud to reduce the number of points."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert IFC to downsampled point clouds with different sampling rates\n", "if IFC_SUPPORT:\n", "    print(\"\\n=== Converting IFC to Downsampled Point Clouds ===\\n\")\n", "    \n", "    # Try different sampling rates\n", "    sampling_rates = [0.5, 0.1, 0.01]\n", "    \n", "    for rate in sampling_rates:\n", "        print(f\"\\n--- Sampling Rate: {rate*100}% ---\")\n", "        \n", "        # Output file for downsampled point cloud\n", "        downsampled_output_file = f\"{output_dir}/ifc_downsampled_{int(rate*100)}percent.ply\"\n", "        \n", "        points, colors = convert_ifc_to_pointcloud(\n", "            ifc_file,\n", "            element_types=None,    # Include all element types\n", "            sampling_rate=rate,    # Use specified sampling rate\n", "            save_to_file=downsampled_output_file,\n", "            visualize=True         # Visualize the point cloud\n", "        )\n", "        \n", "        if points is not None:\n", "            print(f\"Successfully converted IFC to {rate*100}% downsampled point cloud with {points.shape[0]} points\")\n", "            print(f\"Saved point cloud to: {downsampled_output_file}\")\n", "            \n", "            # If the point cloud is still very large, apply additional voxel downsampling\n", "            if points.shape[0] > 100000:  # If more than 100k points\n", "                print(\"Point cloud is still large, applying additional voxel downsampling...\")\n", "                \n", "                # Create Open3D point cloud\n", "                pcd = o3d.geometry.PointCloud()\n", "                pcd.points = o3d.utility.Vector3dVector(points)\n", "                if colors is not None:\n", "                    pcd.colors = o3d.utility.Vector3dVector(colors)\n", "                \n", "                # Apply voxel downsampling\n", "                voxel_size = 0.05  # Adjust based on your model size\n", "                pcd_down = pcd.voxel_down_sample(voxel_size=voxel_size)\n", "                \n", "                # Save voxel downsampled point cloud\n", "                voxel_output_file = f\"{output_dir}/ifc_voxel_downsampled_{int(rate*100)}percent.ply\"\n", "                o3d.io.write_point_cloud(voxel_output_file, pcd_down)\n", "                \n", "                # Display results\n", "                print(f\"Applied voxel downsampling with voxel size {voxel_size}\")\n", "                print(f\"Original points: {points.shape[0]}, After voxel downsampling: {len(pcd_down.points)}\")\n", "                print(f\"Saved voxel downsampled point cloud to: {voxel_output_file}\")\n", "                \n", "                # Visualize voxel downsampled point cloud\n", "                o3d.visualization.draw_geometries([pcd_down], window_name=f\"Voxel Downsampled Point Cloud ({rate*100}%)\")\n", "        else:\n", "            print(f\"Failed to convert IFC to {rate*100}% downsampled point cloud\")\n", "else:\n", "    print(\"IFC support not available. Cannot convert IFC file to point cloud.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON> and Visualize Saved Point Cloud\n", "\n", "You can load and visualize a previously saved point cloud."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_pointcloud(filename):\n", "    \"\"\"\n", "    Loads a point cloud from a file.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the point cloud file (.ply or .pcd format)\n", "        \n", "    Returns:\n", "    --------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Array of shape (N, 3) containing XYZ coordinates\n", "    colors : numpy.ndarray or None\n", "        Array of shape (N, 3) containing RGB colors if available\n", "    \"\"\"\n", "    if not O3D_SUPPORT:\n", "        logger.error(\"Open3D is not available. Cannot load point cloud.\")\n", "        return None, None\n", "    \n", "    try:\n", "        # Load point cloud\n", "        pcd = o3d.io.read_point_cloud(filename)\n", "        \n", "        # Extract points and colors\n", "        points = np.asarray(pcd.points)\n", "        colors = np.asarray(pcd.colors) if pcd.has_colors() else None\n", "        \n", "        logger.info(f\"Loaded point cloud from {filename} with {points.shape[0]} points\")\n", "        return points, colors\n", "    except Exception as e:\n", "        logger.error(f\"Failed to load point cloud: {str(e)}\")\n", "        return None, None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_pointcloud_2d(points, colors=None, title=\"Point Cloud\", sample_size=10000, figsize=(15, 10)):\n", "    \"\"\"\n", "    Visualizes a point cloud using Matplotlib (2D projections).\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Array of shape (N, 3) containing XYZ coordinates\n", "    colors : numpy.ndarray, optional\n", "        Array of shape (N, 3) containing RGB colors\n", "    title : str, optional\n", "        Title for the visualization\n", "    sample_size : int, optional\n", "        Number of points to sample for visualization (to avoid overcrowding)\n", "    figsize : tuple, optional\n", "        Figure size\n", "    \"\"\"\n", "    # Sample points if needed\n", "    if points.shape[0] > sample_size:\n", "        indices = np.random.choice(points.shape[0], sample_size, replace=False)\n", "        sampled_points = points[indices]\n", "        sampled_colors = colors[indices] if colors is not None else None\n", "    else:\n", "        sampled_points = points\n", "        sampled_colors = colors\n", "    \n", "    # Create figure\n", "    fig = plt.figure(figsize=figsize)\n", "    \n", "    # Create 3 subplots for different views\n", "    ax1 = fig.add_subplot(131)\n", "    ax2 = fig.add_subplot(132)\n", "    ax3 = fig.add_subplot(133, projection='3d')\n", "    \n", "    # Set colors\n", "    if sampled_colors is not None:\n", "        point_colors = sampled_colors\n", "    else:\n", "        point_colors = 'blue'\n", "    \n", "    # Plot XY projection (top view)\n", "    ax1.scatter(sampled_points[:, 0], sampled_points[:, 1], c=point_colors, s=1, alpha=0.5)\n", "    ax1.set_title('Top View (XY)')\n", "    ax1.set_xlabel('X')\n", "    ax1.set_ylabel('Y')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Plot XZ projection (front view)\n", "    ax2.scatter(sampled_points[:, 0], sampled_points[:, 2], c=point_colors, s=1, alpha=0.5)\n", "    ax2.set_title('Front View (XZ)')\n", "    ax2.set_xlabel('X')\n", "    ax2.set_ylabel('Z')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # Plot 3D scatter (limited points for performance)\n", "    very_sampled = sampled_points\n", "    if very_sampled.shape[0] > 1000:\n", "        indices = np.random.choice(very_sampled.shape[0], 1000, replace=False)\n", "        very_sampled = very_sampled[indices]\n", "        very_sampled_colors = sampled_colors[indices] if sampled_colors is not None else None\n", "    else:\n", "        very_sampled_colors = sampled_colors\n", "    \n", "    ax3.scatter(\n", "        very_sampled[:, 0], \n", "        very_sampled[:, 1], \n", "        very_sampled[:, 2], \n", "        c=very_sampled_colors if very_sampled_colors is not None else 'blue',\n", "        s=5, alpha=0.7\n", "    )\n", "    ax3.set_title('3D View (Limited Points)')\n", "    ax3.set_xlabel('X')\n", "    ax3.set_ylabel('Y')\n", "    ax3.set_zlabel('Z')\n", "    \n", "    # Set main title\n", "    plt.suptitle(f\"{title} - {points.shape[0]} points\", fontsize=16)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def export_pointcloud_to_html(points, colors=None, title=\"Point Cloud\", sample_size=10000, output_file=\"pointcloud_viewer.html\"):\n", "    \"\"\"\n", "    Exports a point cloud to an HTML file with interactive 3D visualization.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Array of shape (N, 3) containing XYZ coordinates\n", "    colors : numpy.ndarray, optional\n", "        Array of shape (N, 3) containing RGB colors\n", "    title : str, optional\n", "        Title for the visualization\n", "    sample_size : int, optional\n", "        Number of points to sample for visualization (to avoid browser lag)\n", "    output_file : str, optional\n", "        Path to save the HTML file\n", "    \"\"\"\n", "    try:\n", "        import plotly.graph_objects as go\n", "    except ImportError:\n", "        print(\"<PERSON><PERSON><PERSON> is not installed. Installing now...\")\n", "        !pip install plotly\n", "        import plotly.graph_objects as go\n", "    \n", "    # Sample points if needed\n", "    if points.shape[0] > sample_size:\n", "        indices = np.random.choice(points.shape[0], sample_size, replace=False)\n", "        sampled_points = points[indices]\n", "        sampled_colors = colors[indices] if colors is not None else None\n", "    else:\n", "        sampled_points = points\n", "        sampled_colors = colors\n", "    \n", "    # Prepare colors\n", "    if sampled_colors is not None:\n", "        # Convert RGB [0-1] to RGB [0-255]\n", "        color_str = [f'rgb({int(r*255)},{int(g*255)},{int(b*255)})' \n", "                    for r, g, b in sampled_colors]\n", "    else:\n", "        color_str = 'blue'\n", "    \n", "    # Create 3D scatter plot\n", "    fig = go.Figure(data=[go.Scatter3d(\n", "        x=sampled_points[:, 0],\n", "        y=sampled_points[:, 1],\n", "        z=sampled_points[:, 2],\n", "        mode='markers',\n", "        marker=dict(\n", "            size=2,\n", "            color=color_str,\n", "            opacity=0.7\n", "        )\n", "    )])\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        title=f\"{title} - {points.shape[0]} points (showing {sampled_points.shape[0]} sampled points)\",\n", "        scene=dict(\n", "            xaxis_title='X',\n", "            yaxis_title='Y',\n", "            zaxis_title='Z',\n", "            aspectmode='data'\n", "        ),\n", "        width=900,\n", "        height=700,\n", "    )\n", "    \n", "    # Write to HTML file\n", "    fig.write_html(output_file)\n", "    print(f\"Exported interactive 3D visualization to {output_file}\")\n", "    \n", "    # Download the HTML file\n", "    from google.colab import files\n", "    files.download(output_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and visualize a saved point cloud with Matplotlib\n", "print(\"\\n=== Loading and Visualizing Saved Point Cloud with Matplotlib ===\\n\")\n", "\n", "# Check if the file exists\n", "if os.path.exists(output_file):\n", "    points, colors = load_pointcloud(output_file)\n", "    \n", "    if points is not None:\n", "        print(f\"Loaded point cloud with {points.shape[0]} points\")\n", "        \n", "        # Visualize with Matplotlib\n", "        visualize_pointcloud_2d(points, colors, \"IFC Point Cloud\", sample_size=50000)\n", "        \n", "        # Also visualize the structural elements point cloud if it exists\n", "        structural_file = f\"{output_dir}/ifc_structural_elements.ply\"\n", "        if os.path.exists(structural_file):\n", "            struct_points, struct_colors = load_pointcloud(structural_file)\n", "            if struct_points is not None:\n", "                print(f\"Loaded structural elements point cloud with {struct_points.shape[0]} points\")\n", "                visualize_pointcloud_2d(struct_points, struct_colors, \"Structural Elements\", sample_size=20000)\n", "        \n", "        # Also visualize the top elements point cloud if it exists\n", "        top_file = f\"{output_dir}/ifc_top_elements.ply\"\n", "        if os.path.exists(top_file):\n", "            top_points, top_colors = load_pointcloud(top_file)\n", "            if top_points is not None:\n", "                print(f\"Loaded top elements point cloud with {top_points.shape[0]} points\")\n", "                visualize_pointcloud_2d(top_points, top_colors, \"Top Elements\", sample_size=20000)\n", "    else:\n", "        print(\"Failed to load point cloud\")\n", "else:\n", "    print(f\"Point cloud file not found: {output_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export point clouds to HTML for offline viewing\n", "print(\"\\n=== Exporting Point Clouds to HTML for Offline Viewing ===\\n\")\n", "\n", "# Check if the file exists\n", "if os.path.exists(output_file):\n", "    points, colors = load_pointcloud(output_file)\n", "    \n", "    if points is not None:\n", "        print(f\"Loaded point cloud with {points.shape[0]} points\")\n", "        \n", "        # Export to HTML\n", "        export_pointcloud_to_html(\n", "            points, colors, \n", "            title=\"IFC Point Cloud\", \n", "            sample_size=20000, \n", "            output_file=\"ifc_pointcloud_viewer.html\"\n", "        )\n", "        \n", "        # Also export the structural elements point cloud if it exists\n", "        structural_file = f\"{output_dir}/ifc_structural_elements.ply\"\n", "        if os.path.exists(structural_file):\n", "            struct_points, struct_colors = load_pointcloud(structural_file)\n", "            if struct_points is not None:\n", "                print(f\"Loaded structural elements point cloud with {struct_points.shape[0]} points\")\n", "                export_pointcloud_to_html(\n", "                    struct_points, struct_colors, \n", "                    title=\"Structural Elements\", \n", "                    sample_size=10000, \n", "                    output_file=\"ifc_structural_elements_viewer.html\"\n", "                )\n", "        \n", "        # Also export the top elements point cloud if it exists\n", "        top_file = f\"{output_dir}/ifc_top_elements.ply\"\n", "        if os.path.exists(top_file):\n", "            top_points, top_colors = load_pointcloud(top_file)\n", "            if top_points is not None:\n", "                print(f\"Loaded top elements point cloud with {top_points.shape[0]} points\")\n", "                export_pointcloud_to_html(\n", "                    top_points, top_colors, \n", "                    title=\"Top Elements\", \n", "                    sample_size=10000, \n", "                    output_file=\"ifc_top_elements_viewer.html\"\n", "                )\n", "    else:\n", "        print(\"Failed to load point cloud\")\n", "else:\n", "    print(f\"Point cloud file not found: {output_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and visualize a saved point cloud with Open3D (may not work in Colab)\n", "if O3D_SUPPORT:\n", "    print(\"\\n=== Loading and Visualizing Saved Point Cloud with Open3D ===\\n\")\n", "    print(\"Note: This may not work in <PERSON><PERSON>'s headless environment.\")\n", "    \n", "    # Check if the file exists\n", "    if os.path.exists(output_file):\n", "        points, colors = load_pointcloud(output_file)\n", "        \n", "        if points is not None:\n", "            print(f\"Loaded point cloud with {points.shape[0]} points\")\n", "            visualize_pointcloud(points, colors, \"Loaded Point Cloud\")\n", "        else:\n", "            print(\"Failed to load point cloud\")\n", "    else:\n", "        print(f\"Point cloud file not found: {output_file}\")\n", "else:\n", "    print(\"Open3D support not available. Cannot load and visualize point cloud.\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: ifcopenshell in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (0.8.2)\n", "Requirement already satisfied: open3d in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (0.19.0)\n", "Requirement already satisfied: shapely in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (2.1.1)\n", "Requirement already satisfied: numpy in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (1.26.4)\n", "Requirement already satisfied: isodate in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (0.7.2)\n", "Requirement already satisfied: python-dateutil in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (2.9.0.post0)\n", "Requirement already satisfied: lark in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (1.2.2)\n", "Requirement already satisfied: typing-extensions in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (4.14.0)\n", "Requirement already satisfied: dash>=2.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.0.4)\n", "Requirement already satisfied: werkzeug>=3.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.0.6)\n", "Requirement already satisfied: flask>=3.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.0.3)\n", "Requirement already satisfied: nbformat>=5.7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from open3d) (5.10.4)\n", "Requirement already satisfied: configargparse in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (1.7.1)\n", "Requirement already satisfied: addict in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (2.4.0)\n", "Requirement already satisfied: pillow>=9.3.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (11.2.1)\n", "Requirement already satisfied: matplotlib>=3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.10.3)\n", "Requirement already satisfied: pandas>=1.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (2.3.0)\n", "Requirement already satisfied: pyyaml>=5.4.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (6.0.2)\n", "Requirement already satisfied: scikit-learn>=0.21 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (1.7.0)\n", "Requirement already satisfied: tqdm in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (4.67.1)\n", "Requirement already satisfied: pyquaternion in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (0.9.9)\n", "Requirement already satisfied: plotly>=5.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (6.1.2)\n", "Requirement already satisfied: importlib-metadata in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (8.7.0)\n", "Requirement already satisfied: requests in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (2.32.4)\n", "Requirement already satisfied: retrying in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (1.3.5)\n", "Requirement already satisfied: nest-asyncio in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (1.6.0)\n", "Requirement already satisfied: setuptools in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (80.9.0)\n", "Requirement already satisfied: Jinja2>=3.1.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (3.1.6)\n", "Requirement already satisfied: itsdangerous>=2.1.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (2.2.0)\n", "Requirement already satisfied: click>=8.1.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (8.2.1)\n", "Requirement already satisfied: blinker>=1.6.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (1.9.0)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from werkzeug>=3.0.0->open3d) (3.0.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (25.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib>=3->open3d) (3.2.3)\n", "Requirement already satisfied: fastjsonschema>=2.15 in /Users/<USER>/.local/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (2.21.1)\n", "Requirement already satisfied: jsonschema>=2.6 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (4.24.0)\n", "Requirement already satisfied: jupyter-core!=5.0.*,>=4.12 in /Users/<USER>/.local/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (5.7.2)\n", "Requirement already satisfied: traitlets>=5.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (5.14.3)\n", "Requirement already satisfied: attrs>=22.2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.25.1)\n", "Requirement already satisfied: platformdirs>=2.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jupyter-core!=5.0.*,>=4.12->nbformat>=5.7.0->open3d) (4.3.8)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas>=1.0->open3d) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas>=1.0->open3d) (2025.2)\n", "Requirement already satisfied: narwhals>=1.15.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from plotly>=5.0.0->dash>=2.6.0->open3d) (1.44.0)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from python-dateutil->ifcopenshell) (1.17.0)\n", "Requirement already satisfied: scipy>=1.8.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from scikit-learn>=0.21->open3d) (1.16.0)\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from scikit-learn>=0.21->open3d) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from scikit-learn>=0.21->open3d) (3.6.0)\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from importlib-metadata->dash>=2.6.0->open3d) (3.23.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests->dash>=2.6.0->open3d) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests->dash>=2.6.0->open3d) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests->dash>=2.6.0->open3d) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests->dash>=2.6.0->open3d) (2025.6.15)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Converting IFC file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Environment Initialized\n", "Analysis Date: 2025-07-02 15:15:30\n", "IFC file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "Output directory: ../../data/output_runs/foundation_analysis/castro_area4/preprocessing\n", "Output PLY: ../../data/output_runs/foundation_analysis/castro_area4/preprocessing/ifc_pointcloud.ply\n", "Output XYZ: ../../data/output_runs/foundation_analysis/castro_area4/preprocessing/ifc_pointcloud.xyz\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:__main__:No points extracted from IfcBuildingElement. Checking all products for tessellation.\n", "INFO:__main__:Extracted 240 points from IFC\n", "INFO:__main__:Saved point cloud to ../../data/output_runs/foundation_analysis/castro_area4/preprocessing/ifc_pointcloud.xyz\n", "INFO:__main__:Saved point cloud to ../../data/output_runs/foundation_analysis/castro_area4/preprocessing/ifc_pointcloud.ply\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Conversion completed. Total points: 240\n"]}], "source": ["# %% [markdown]\n", "# # IFC to Point Cloud Conversion\n", "#\n", "# This notebook converts an IFC file to a point cloud for use in alignment tasks with drone-generated point clouds. It extracts geometric vertices from the IFC model, handling various representation types and adding robustness for missing data.\n", "#\n", "# **Stage**: Preprocessing  \n", "# **Input Data**: IFC file  \n", "# **Output**: Point cloud in PLY and XYZ formats  \n", "# **Method**: IfcOpenShell for geometry extraction  \n", "#\n", "# **Author**: <PERSON><PERSON><PERSON>  \n", "# **Date**: July 02, 2025  \n", "# **Project**: As-Built Foundation Analysis\n", "\n", "# %% [markdown]\n", "# ## 1. Environment Setup\n", "#\n", "# Configure the environment with required libraries for IFC processing.\n", "\n", "# %%\n", "# Install required package\n", "!pip install ifcopenshell open3d\n", "\n", "# %%\n", "# Import libraries\n", "import numpy as np\n", "import os\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "import ifcopenshell\n", "import open3d as o3d\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"Environment Initialized\")\n", "print(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "\n", "# %% [markdown]\n", "# ## 2. Configuration Parameters\n", "#\n", "# Define parameters for IFC conversion and output paths.\n", "\n", "# %%\n", "# Configuration parameters\n", "ifc_file = \"/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc\"\n", "output_dir = \"../../data/output_runs/foundation_analysis/castro_area4/preprocessing\"\n", "output_ply = \"ifc_pointcloud.ply\"\n", "output_xyz = \"ifc_pointcloud.xyz\"\n", "\n", "# Setup paths\n", "base_path = Path('../..')\n", "data_path = base_path / 'data'\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "full_ply_path = output_path / output_ply\n", "full_xyz_path = output_path / output_xyz\n", "\n", "print(f\"IFC file: {ifc_file}\")\n", "print(f\"Output directory: {output_path}\")\n", "print(f\"Output PLY: {full_ply_path}\")\n", "print(f\"Output XYZ: {full_xyz_path}\")\n", "\n", "# %% [markdown]\n", "# ## 3. IFC to Point Cloud Conversion\n", "#\n", "# Convert the IFC file to a point cloud by extracting vertices from building elements, with enhanced representation handling.\n", "\n", "# %%\n", "def convert_ifc_to_pointcloud(ifc_path, output_ply_path, output_xyz_path):\n", "    \"\"\"Converts an IFC file to a point cloud and saves in PLY and XYZ formats.\"\"\"\n", "    logger.info(f\"Converting IFC file: {ifc_path}\")\n", "    try:\n", "        ifc = ifcopenshell.open(ifc_path)\n", "        points = []\n", "        for element in ifc.by_type(\"IfcBuildingElement\"):\n", "            if hasattr(element, \"Representation\") and element.Representation is not None:\n", "                for rep in element.Representation.Representations:\n", "                    if rep is not None:\n", "                        for item in rep.Items:\n", "                            if item is not None:\n", "                                # Handle different representation types\n", "                                if item.is_a(\"IfcFacetedBrep\"):\n", "                                    for face in item.Outer.CfsFaces:\n", "                                        for loop in face.Bounds:\n", "                                            for vertex in loop.Bound.Polygon:\n", "                                                points.append(vertex.Coordinates)\n", "                                elif item.is_a(\"IfcExtrudedAreaSolid\"):\n", "                                    if hasattr(item, \"SweptArea\") and item.SweptArea.is_a(\"IfcArbitraryClosedProfileDef\"):\n", "                                        for point in item.SweptArea.OuterCurve.Points:\n", "                                            points.append([point.X, point.Y, 0.0])  # Z=0, adjust if needed\n", "                                elif item.is_a(\"IfcTessellatedFaceSet\"):\n", "                                    for coord in item.Coordinates.CoordList:\n", "                                        points.append(coord)\n", "                                elif item.is_a(\"IfcTriangulatedFaceSet\"):\n", "                                    for coord in item.Coordinates.CoordList:\n", "                                        points.append(coord)\n", "                                elif item.is_a(\"IfcMappedItem\"):\n", "                                    # Handle mapped geometry by accessing the mapping source\n", "                                    if hasattr(item, \"MappingSource\") and item.MappingSource.MappedRepresentation:\n", "                                        for mapped_item in item.MappingSource.MappedRepresentation.Items:\n", "                                            if mapped_item.is_a(\"IfcTessellatedFaceSet\"):\n", "                                                for coord in mapped_item.Coordinates.CoordList:\n", "                                                    points.append(coord)\n", "\n", "        if not points:\n", "            logger.warning(\"No points extracted from IfcBuildingElement. Checking all products for tessellation.\")\n", "            for element in ifc.by_type(\"IfcProduct\"):\n", "                if hasattr(element, \"Representation\") and element.Representation is not None:\n", "                    for rep in element.Representation.Representations:\n", "                        if rep is not None and rep.RepresentationType == \"Tessellation\":\n", "                            for item in rep.Items:\n", "                                if item is not None and item.is_a(\"IfcTessellatedFaceSet\"):\n", "                                    for coord in item.Coordinates.CoordList:\n", "                                        points.append(coord)\n", "                                elif item.is_a(\"IfcTriangulatedFaceSet\"):\n", "                                    for coord in item.Coordinates.CoordList:\n", "                                        points.append(coord)\n", "\n", "        points = np.array(points) if points else np.array([])\n", "        logger.info(f\"Extracted {points.shape[0]} points from IFC\")\n", "\n", "        if points.size == 0:\n", "            raise ValueError(\"No valid geometric data found in IFC file. Check representation types or file integrity.\")\n", "\n", "        # Save as XYZ\n", "        np.savetxt(str(output_xyz_path), points, fmt=\"%f %f %f\", delimiter=\" \")\n", "        logger.info(f\"Saved point cloud to {output_xyz_path}\")\n", "\n", "        # Convert to Open3D point cloud and save as PLY\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        o3d.io.write_point_cloud(str(output_ply_path), pcd)\n", "        logger.info(f\"Saved point cloud to {output_ply_path}\")\n", "        return points\n", "    except Exception as e:\n", "        logger.error(f\"Error converting IFC to point cloud: {e}\")\n", "        return None\n", "\n", "# Perform conversion\n", "source_points = convert_ifc_to_pointcloud(ifc_file, full_ply_path, full_xyz_path)\n", "if source_points is None:\n", "    raise ValueError(\"Failed to convert IFC to point cloud\")\n", "\n", "print(f\"Conversion completed. Total points: {source_points.shape[0]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Conclusion\n", "\n", "This notebook has demonstrated how to:\n", "\n", "1. Load IFC files and extract geometry\n", "2. Convert IFC elements to point clouds\n", "3. Visualize the resulting point clouds\n", "4. Save point clouds to various formats (PLY, PCD)\n", "5. Filter IFC elements by type\n", "6. Downsample large point clouds\n", "7. Load and visualize saved point clouds\n", "\n", "You can use these tools to convert your IFC files to point clouds for further processing, analysis, or visualization."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}