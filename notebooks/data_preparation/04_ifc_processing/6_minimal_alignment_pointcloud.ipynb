{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Minimal IFC to Point Cloud for Alignment\n", "\n", "This notebook creates a lightweight, alignment-ready point cloud from IFC files using minimal processing.\n", "\n", "**Focus**: Bare minimum points needed for successful alignment with drone/geotiff data\n", "\n", "**Strategy**:\n", "- Process only essential elements (walls, roofs, slabs)\n", "- Use simple vertex extraction (fast)\n", "- Smart sampling to avoid memory issues\n", "- Target: 5,000-15,000 points for alignment\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** July 2025"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON>up"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import logging\n", "import numpy as np\n", "from pathlib import Path\n", "from collections import defaultdict\n", "import time\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check ifcopenshell\n", "try:\n", "    import ifcopenshell\n", "    import ifcopenshell.geom\n", "    logger.info(f\"ifcopenshell version: {ifcopenshell.version}\")\n", "except ImportError:\n", "    logger.error(\"ifcopenshell not available\")\n", "    raise\n", "\n", "# Optional Open3D for visualization\n", "try:\n", "    import open3d as o3d\n", "    O3D_AVAILABLE = True\n", "    logger.info(\"Open3D available for visualization\")\n", "except ImportError:\n", "    O3D_AVAILABLE = False\n", "    logger.info(\"Open3D not available - will use manual PLY export\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration for Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Minimal configuration for alignment\n", "ALIGNMENT_CONFIG = {\n", "    # Essential elements for alignment (what drones see)\n", "    'priority_elements': [\n", "        'IfcWall',                    # Exterior walls - CRITICAL\n", "        'IfcRoof',                    # Roofs - CRITICAL\n", "        'IfcSlab',                    # Floor slabs - IMPORTANT\n", "        'IfcCurtainWall',            # Curtain walls - IMPORTANT\n", "        'IfcBuildingElementProxy',    # Generic elements - MEDIUM\n", "        'IfcColumn'                   # Columns - MEDIUM\n", "    ],\n", "    \n", "    # Sampling strategy\n", "    'max_elements_per_type': 50,     # Limit elements to avoid memory issues\n", "    'target_points_per_element': 100, # Points per element\n", "    'max_total_points': 15000,       # Total point limit\n", "    'min_points_for_alignment': 3000, # Minimum viable points\n", "    \n", "    # Processing options\n", "    'use_world_coords': True,\n", "    'skip_small_elements': True,     # Skip elements < 1m²\n", "    'prioritize_large_elements': True # Process largest elements first\n", "}\n", "\n", "logger.info(\"Minimal alignment configuration loaded\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Paths\n", "base_path = Path(\"../../../data/raw/ifc_files\")\n", "output_path = Path(\"../../../data/processed/minimal_alignment_pointclouds\")\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Find IFC files\n", "ifc_files = list(base_path.glob(\"*.ifc\"))\n", "logger.info(f\"Found {len(ifc_files)} IFC files\")\n", "for ifc_file in ifc_files:\n", "    logger.info(f\"  - {ifc_file.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Minimal Processing Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_element_bounds(element, settings):\n", "    \"\"\"\n", "    Get approximate bounds of element to filter small ones\n", "    \"\"\"\n", "    try:\n", "        shape = ifcopenshell.geom.create_shape(settings, element)\n", "        if not shape or not shape.geometry:\n", "            return None\n", "        \n", "        verts = shape.geometry.verts\n", "        if not verts:\n", "            return None\n", "        \n", "        points = np.array(verts).reshape(-1, 3)\n", "        bounds = {\n", "            'min': points.min(axis=0),\n", "            'max': points.max(axis=0),\n", "            'size': points.max(axis=0) - points.min(axis=0)\n", "        }\n", "        \n", "        # Estimate area (rough)\n", "        bounds['area'] = bounds['size'][0] * bounds['size'][1]\n", "        \n", "        return bounds\n", "        \n", "    except Exception:\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_minimal_points(element, settings, target_points=100):\n", "    \"\"\"\n", "    Extract minimal points from element for alignment\n", "    \"\"\"\n", "    try:\n", "        shape = ifcopenshell.geom.create_shape(settings, element)\n", "        if not shape or not shape.geometry:\n", "            return None\n", "        \n", "        verts = shape.geometry.verts\n", "        if not verts:\n", "            return None\n", "        \n", "        # Convert to points\n", "        points = np.array(verts).reshape(-1, 3)\n", "        \n", "        # Sample points if too many\n", "        if len(points) > target_points:\n", "            indices = np.random.choice(len(points), target_points, replace=False)\n", "            points = points[indices]\n", "        \n", "        return {\n", "            'points': points,\n", "            'element_type': element.is_a(),\n", "            'element_id': element.GlobalId,\n", "            'point_count': len(points)\n", "        }\n", "        \n", "    except Exception as e:\n", "        logger.debug(f\"Error extracting points from {element.GlobalId}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_minimal_alignment_pointcloud(ifc_file_path, config=ALIGNMENT_CONFIG):\n", "    \"\"\"\n", "    Create minimal point cloud suitable for alignment\n", "    \"\"\"\n", "    logger.info(f\"Processing {ifc_file_path.name} for minimal alignment point cloud\")\n", "    \n", "    try:\n", "        # Load IFC\n", "        ifc_model = ifcopenshell.open(str(ifc_file_path))\n", "        \n", "        # Setup geometry settings (minimal)\n", "        settings = ifcopenshell.geom.settings()\n", "        if config['use_world_coords']:\n", "            settings.set(settings.USE_WORLD_COORDS, True)\n", "        \n", "        # Collect elements by priority\n", "        priority_elements = []\n", "        element_stats = defaultdict(int)\n", "        \n", "        for element_type in config['priority_elements']:\n", "            elements = ifc_model.by_type(element_type)\n", "            elements = [e for e in elements if e.Representation]  # Only with geometry\n", "            \n", "            logger.info(f\"Found {len(elements)} {element_type} elements\")\n", "            element_stats[element_type] = len(elements)\n", "            \n", "            # Filter and sort by size if needed\n", "            if config['skip_small_elements'] or config['prioritize_large_elements']:\n", "                elements_with_bounds = []\n", "                for elem in elements[:config['max_elements_per_type']]:\n", "                    bounds = get_element_bounds(elem, settings)\n", "                    if bounds and bounds['area'] > 1.0:  # Skip < 1m²\n", "                        elements_with_bounds.append((elem, bounds['area']))\n", "                \n", "                # Sort by area (largest first)\n", "                if config['prioritize_large_elements']:\n", "                    elements_with_bounds.sort(key=lambda x: x[1], reverse=True)\n", "                \n", "                elements = [elem for elem, area in elements_with_bounds]\n", "            \n", "            # Limit elements per type\n", "            elements = elements[:config['max_elements_per_type']]\n", "            priority_elements.extend(elements)\n", "        \n", "        logger.info(f\"Processing {len(priority_elements)} priority elements\")\n", "        \n", "        # Extract points\n", "        all_points = []\n", "        all_labels = []\n", "        element_type_map = {}\n", "        current_label = 0\n", "        points_per_type = defaultdict(int)\n", "        \n", "        for i, element in enumerate(priority_elements):\n", "            if i % 20 == 0:\n", "                logger.info(f\"Progress: {i}/{len(priority_elements)}\")\n", "            \n", "            # Check point limit\n", "            if len(all_points) > 0 and sum(len(pts) for pts in all_points) > config['max_total_points']:\n", "                logger.info(f\"Reached point limit ({config['max_total_points']}), stopping\")\n", "                break\n", "            \n", "            point_data = extract_minimal_points(element, settings, config['target_points_per_element'])\n", "            \n", "            if point_data is None:\n", "                continue\n", "            \n", "            # Assign label\n", "            element_type = point_data['element_type']\n", "            if element_type not in element_type_map:\n", "                element_type_map[element_type] = current_label\n", "                current_label += 1\n", "            \n", "            label = element_type_map[element_type]\n", "            points = point_data['points']\n", "            \n", "            all_points.append(points)\n", "            all_labels.extend([label] * len(points))\n", "            points_per_type[element_type] += len(points)\n", "        \n", "        # Combine results\n", "        if all_points:\n", "            combined_points = np.vstack(all_points)\n", "            combined_labels = np.array(all_labels)\n", "        else:\n", "            combined_points = np.array([]).reshape(0, 3)\n", "            combined_labels = np.array([])\n", "        \n", "        logger.info(f\"Generated {len(combined_points)} points for alignment\")\n", "        logger.info(\"Points by element type:\")\n", "        for elem_type, count in points_per_type.items():\n", "            logger.info(f\"  {elem_type}: {count}\")\n", "        \n", "        return {\n", "            'points': combined_points,\n", "            'labels': combined_labels,\n", "            'element_type_map': element_type_map,\n", "            'points_per_type': dict(points_per_type),\n", "            'element_stats': dict(element_stats),\n", "            'suitable_for_alignment': len(combined_points) >= config['min_points_for_alignment']\n", "        }\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error processing IFC file: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Export Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_minimal_pointcloud(result, output_file):\n", "    \"\"\"\n", "    Save minimal point cloud in multiple formats\n", "    \"\"\"\n", "    points = result['points']\n", "    labels = result['labels']\n", "    \n", "    if len(points) == 0:\n", "        logger.warning(\"No points to save\")\n", "        return\n", "    \n", "    try:\n", "        # Save with Open3D if available\n", "        if O3D_AVAILABLE:\n", "            pcd = o3d.geometry.PointCloud()\n", "            pcd.points = o3d.utility.Vector3dVector(points)\n", "            \n", "            # Add colors based on element types\n", "            colors = generate_simple_colors(labels)\n", "            pcd.colors = o3d.utility.Vector3dVector(colors)\n", "            \n", "            o3d.io.write_point_cloud(str(output_file), pcd)\n", "            logger.info(f\"Saved point cloud to {output_file.name}\")\n", "        \n", "        # Always save manual PLY as backup\n", "        ply_file = output_file.with_suffix('.manual.ply')\n", "        save_ply_manual(points, labels, ply_file)\n", "        \n", "        # Save simple XYZ format\n", "        xyz_file = output_file.with_suffix('.xyz')\n", "        save_xyz_format(points, labels, xyz_file)\n", "        \n", "        # Save metadata\n", "        metadata_file = output_file.with_suffix('.info.txt')\n", "        save_metadata(result, metadata_file)\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error saving point cloud: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_simple_colors(labels):\n", "    \"\"\"\n", "    Generate simple colors for visualization\n", "    \"\"\"\n", "    unique_labels = np.unique(labels)\n", "    colors = np.zeros((len(labels), 3))\n", "    \n", "    # Simple color palette\n", "    color_palette = [\n", "        [1.0, 0.0, 0.0],  # Red\n", "        [0.0, 1.0, 0.0],  # Green\n", "        [0.0, 0.0, 1.0],  # Blue\n", "        [1.0, 1.0, 0.0],  # Yellow\n", "        [1.0, 0.0, 1.0],  # <PERSON><PERSON>a\n", "        [0.0, 1.0, 1.0],  # <PERSON><PERSON>\n", "        [0.5, 0.5, 0.5],  # <PERSON>\n", "    ]\n", "    \n", "    for i, label in enumerate(unique_labels):\n", "        color_idx = i % len(color_palette)\n", "        mask = labels == label\n", "        colors[mask] = color_palette[color_idx]\n", "    \n", "    return colors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_ply_manual(points, labels, output_file):\n", "    \"\"\"\n", "    Save PLY file manually (no dependencies)\n", "    \"\"\"\n", "    try:\n", "        with open(output_file, 'w') as f:\n", "            # PLY header\n", "            f.write(\"ply\\n\")\n", "            f.write(\"format ascii 1.0\\n\")\n", "            f.write(f\"element vertex {len(points)}\\n\")\n", "            f.write(\"property float x\\n\")\n", "            f.write(\"property float y\\n\")\n", "            f.write(\"property float z\\n\")\n", "            f.write(\"property int label\\n\")\n", "            f.write(\"end_header\\n\")\n", "            \n", "            # Data\n", "            for point, label in zip(points, labels):\n", "                f.write(f\"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {int(label)}\\n\")\n", "        \n", "        logger.info(f\"Saved manual PLY to {output_file.name}\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error saving manual PLY: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_xyz_format(points, labels, output_file):\n", "    \"\"\"\n", "    Save simple XYZ format\n", "    \"\"\"\n", "    try:\n", "        with open(output_file, 'w') as f:\n", "            for point, label in zip(points, labels):\n", "                f.write(f\"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {int(label)}\\n\")\n", "        \n", "        logger.info(f\"Saved XYZ format to {output_file.name}\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error saving XYZ: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_metadata(result, output_file):\n", "    \"\"\"\n", "    Save alignment metadata\n", "    \"\"\"\n", "    try:\n", "        with open(output_file, 'w') as f:\n", "            f.write(\"MINIMAL ALIGNMENT POINT CLOUD METADATA\\n\")\n", "            f.write(\"=\"*50 + \"\\n\\n\")\n", "            \n", "            f.write(f\"Total Points: {len(result['points']):,}\\n\")\n", "            f.write(f\"Suitable for Alignment: {result['suitable_for_alignment']}\\n\\n\")\n", "            \n", "            if len(result['points']) > 0:\n", "                points = result['points']\n", "                f.write(\"Coordinate Bounds:\\n\")\n", "                f.write(f\"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]\\n\")\n", "                f.write(f\"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]\\n\")\n", "                f.write(f\"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]\\n\\n\")\n", "            \n", "            f.write(\"Element Types Found:\\n\")\n", "            for elem_type, count in result['element_stats'].items():\n", "                f.write(f\"  {elem_type}: {count} elements\\n\")\n", "            \n", "            f.write(\"\\nPoints per Element Type:\\n\")\n", "            for elem_type, count in result['points_per_type'].items():\n", "                f.write(f\"  {elem_type}: {count:,} points\\n\")\n", "            \n", "            f.write(\"\\nElement Type Labels:\\n\")\n", "            for elem_type, label in result['element_type_map'].items():\n", "                f.write(f\"  {label}: {elem_type}\\n\")\n", "        \n", "        logger.info(f\"Saved metadata to {output_file.name}\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error saving metadata: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Execute Minimal Conversion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process IFC file for minimal alignment point cloud\n", "if not ifc_files:\n", "    logger.error(\"No IFC files found\")\n", "else:\n", "    ifc_file = ifc_files[0]\n", "    logger.info(f\"Creating minimal alignment point cloud from: {ifc_file.name}\")\n", "    \n", "    # Process with minimal settings\n", "    result = create_minimal_alignment_pointcloud(ifc_file, ALIGNMENT_CONFIG)\n", "    \n", "    if result and len(result['points']) > 0:\n", "        logger.info(\"\\n=== MINIMAL ALIGNMENT POINT CLOUD RESULTS ===\")\n", "        logger.info(f\"Total points generated: {len(result['points']):,}\")\n", "        logger.info(f\"Suitable for alignment: {result['suitable_for_alignment']}\")\n", "        logger.info(f\"Element types included: {len(result['element_type_map'])}\")\n", "        \n", "        # Show point distribution\n", "        logger.info(\"\\nPoint distribution:\")\n", "        for elem_type, count in result['points_per_type'].items():\n", "            percentage = (count / len(result['points'])) * 100\n", "            logger.info(f\"  {elem_type}: {count:,} points ({percentage:.1f}%)\")\n", "        \n", "        # Coordinate summary\n", "        points = result['points']\n", "        logger.info(\"\\nCoordinate ranges:\")\n", "        logger.info(f\"  X: [{points[:, 0].min():.1f}, {points[:, 0].max():.1f}] (span: {points[:, 0].max() - points[:, 0].min():.1f}m)\")\n", "        logger.info(f\"  Y: [{points[:, 1].min():.1f}, {points[:, 1].max():.1f}] (span: {points[:, 1].max() - points[:, 1].min():.1f}m)\")\n", "        logger.info(f\"  Z: [{points[:, 2].min():.1f}, {points[:, 2].max():.1f}] (span: {points[:, 2].max() - points[:, 2].min():.1f}m)\")\n", "        \n", "        # Save point cloud\n", "        output_file = output_path / f\"{ifc_file.stem}_minimal_alignment.ply\"\n", "        save_minimal_pointcloud(result, output_file)\n", "        \n", "        # Alignment assessment\n", "        logger.info(\"\\n=== ALIGNMENT READINESS ASSESSMENT ===\")\n", "        if result['suitable_for_alignment']:\n", "            logger.info(\"✅ READY for alignment with drone/geotiff point clouds\")\n", "            logger.info(\"✅ Sufficient point count for ICP registration\")\n", "            logger.info(\"✅ Good element type coverage\")\n", "        else:\n", "            logger.info(\"⚠️  May need more points for robust alignment\")\n", "            logger.info(f\"   Current: {len(result['points'])} points\")\n", "            logger.info(f\"   Recommended: {ALIGNMENT_CONFIG['min_points_for_alignment']}+ points\")\n", "        \n", "        # Recommendations\n", "        logger.info(\"\\nRecommendations for alignment:\")\n", "        if 'IfcWall' in result['points_per_type']:\n", "            logger.info(\"✅ Walls included - good for building outline alignment\")\n", "        else:\n", "            logger.info(\"⚠️  No walls found - may affect alignment quality\")\n", "        \n", "        if 'IfcRoof' in result['points_per_type']:\n", "            logger.info(\"✅ Roofs included - good for aerial alignment\")\n", "        else:\n", "            logger.info(\"⚠️  No roofs found - may affect aerial alignment\")\n", "        \n", "        logger.info(\"\\nNext steps:\")\n", "        logger.info(\"1. Load this point cloud as reference in alignment software\")\n", "        logger.info(\"2. Use ICP or feature-based registration\")\n", "        logger.info(\"3. Focus on structural elements (walls, roofs) for key features\")\n", "        \n", "    else:\n", "        logger.error(\"Failed to generate minimal alignment point cloud\")\n", "        logger.info(\"Try adjusting ALIGNMENT_CONFIG parameters:\")\n", "        logger.info(\"- Increase max_elements_per_type\")\n", "        logger.info(\"- Increase target_points_per_element\")\n", "        logger.info(\"- Set skip_small_elements to False\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Quick Visualization (Optional)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simple 2D visualization if matplotlib available\n", "try:\n", "    import matplotlib.pyplot as plt\n", "    \n", "    if 'result' in locals() and result and len(result['points']) > 0:\n", "        points = result['points']\n", "        labels = result['labels']\n", "        \n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "        \n", "        # XY projection (top view)\n", "        scatter = ax1.scatter(points[:, 0], points[:, 1], c=labels, s=1, alpha=0.6)\n", "        ax1.set_xlabel('X (m)')\n", "        ax1.set_ylabel('Y (m)')\n", "        ax1.set_title('Top View (XY)')\n", "        ax1.grid(True, alpha=0.3)\n", "        ax1.set_aspect('equal')\n", "        \n", "        # XZ projection (side view)\n", "        ax2.scatter(points[:, 0], points[:, 2], c=labels, s=1, alpha=0.6)\n", "        ax2.set_xlabel('X (m)')\n", "        ax2.set_ylabel('Z (m)')\n", "        ax2.set_title('Side View (XZ)')\n", "        ax2.grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        logger.info(\"2D visualization complete\")\n", "    \n", "except ImportError:\n", "    logger.info(\"Matplotlib not available - skipping visualization\")\n", "except Exception as e:\n", "    logger.warning(f\"Visualization error: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}