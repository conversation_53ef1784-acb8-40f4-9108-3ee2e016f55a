import ifcopenshell
import ifcopenshell.geom
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.express as px
from pathlib import Path
import open3d as o3d



# 1. Load IFC and count elements
def load_ifc_metadata(ifc_path):
    model = ifcopenshell.open(ifc_path)
    elements = model.by_type("IfcProduct")
    counts = {}
    for elem in elements:
        typename = elem.is_a()
        counts[typename] = counts.get(typename, 0) + 1
    return counts



# 2. Create conversion strategy (simple sampling rule)
def create_strategy(counts, sample_per_elem=20):
    return {k: min(v, 10000) * sample_per_elem for k, v in counts.items() if "Ifc" in k}



# 3. Convert to XYZ points using geometry engine
def convert_to_point_cloud(ifc_path, strategy):
    model = ifcopenshell.open(ifc_path)
    settings = ifcopenshell.geom.settings()
    settings.set(settings.USE_WORLD_COORDS, True)

    points = []
    for ifc_type, est_points in strategy.items():
        elems = model.by_type(ifc_type)
        for elem in elems:
            try:
                shape = ifcopenshell.geom.create_shape(settings, elem)
                verts = np.array(shape.geometry.verts).reshape(-1, 3)
                points.append(verts)
            except:
                continue
    return np.vstack(points) if points else np.array([])


# 4. Visualizations
def visualize_2d(points):
    if points.shape[0] == 0:
        print("No points to visualize.")
        return
    plt.figure(figsize=(8, 6))
    plt.scatter(points[:, 0], points[:, 1], s=0.5, alpha=0.6)
    plt.title("2D View (X-Y Plane)")
    plt.xlabel("X")
    plt.ylabel("Y")
    plt.axis("equal")
    plt.grid(True)
    plt.show()

def visualize_3d(points):
    if points.shape[0] == 0:
        print("No points to visualize.")
        return
    df = pd.DataFrame(points, columns=["x", "y", "z"])
    fig = px.scatter_3d(df.sample(min(len(df), 15000)), x='x', y='y', z='z', opacity=0.6)
    fig.update_layout(title="3D Point Cloud", height=700)
    fig.show()


# 5. Run full flow
if __name__ == "__main__":
    ifc_file = "GRE.EEC.S.00.IT.P.14353.00.265.ifc"  # Change to your IFC file
    ifc_path = Path("../../../data/raw/trino_enel/ifc") / ifc_file

    counts = load_ifc_metadata(str(ifc_path))
    strategy = create_strategy(counts)
    points = convert_to_point_cloud(str(ifc_path), strategy)

    print(f"✅ Total points: {points.shape[0]}")
    visualize_2d(points)
    visualize_3d(points)

