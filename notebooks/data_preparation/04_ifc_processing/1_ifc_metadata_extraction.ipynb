import os
import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Check for ifcopenshell
try:
    import ifcopenshell
    import ifcopenshell.util.element
    import ifcopenshell.util.placement
    IFC_SUPPORT = True
    logger.info("ifcopenshell is available")
except ImportError:
    IFC_SUPPORT = False
    logger.error("ifcopenshell is not installed")
    logger.info("Install with: pip install ifcopenshell")

!ls ../../../data/raw/trino_enel/ifc/

# Set up paths
base_path = Path("../../../data/raw/trino_enel/ifc")
output_path = Path("../../../data/processed/trino_enel/ifc_metadata")
output_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Base path: {base_path}")
logger.info(f"Output path: {output_path}")

# Find IFC files
ifc_files = list(base_path.glob("*.ifc"))
logger.info(f"Found {len(ifc_files)} IFC files")
for ifc_file in ifc_files:
    logger.info(f"  - {ifc_file.name}")

# Load first IFC file
if not IFC_SUPPORT:
    logger.error("Cannot proceed without ifcopenshell")
    ifc_model = None
elif not ifc_files:
    logger.error("No IFC files found")
    ifc_model = None
else:
    ifc_file_path = ifc_files[0]
    logger.info(f"Loading IFC file: {ifc_file_path.name}")
    
    try:
        ifc_model = ifcopenshell.open(str(ifc_file_path))
        logger.info(f"Successfully loaded IFC file")
        logger.info(f"IFC schema: {ifc_model.schema}")
    except Exception as e:
        logger.error(f"Failed to load IFC file: {e}")
        ifc_model = None

# Extract project information
if ifc_model:
    projects = ifc_model.by_type('IfcProject')
    logger.info(f"Found {len(projects)} projects")
    
    project_info = []
    for project in projects:
        info = {
            'GlobalId': project.GlobalId,
            'Name': project.Name,
            'Description': project.Description,
            'Phase': project.Phase,
            'LongName': getattr(project, 'LongName', None)
        }
        project_info.append(info)
        logger.info(f"Project: {info['Name']} - {info['Description']}")
    
    # Save project information
    if project_info:
        df_projects = pd.DataFrame(project_info)
        output_file = output_path / f"{ifc_file_path.stem}_projects.csv"
        df_projects.to_csv(output_file, index=False)
        logger.info(f"Saved project information to {output_file.name}")
else:
    logger.warning("No IFC model loaded")

# Extract spatial hierarchy
if ifc_model:
    spatial_elements = ifc_model.by_type('IfcSpatialStructureElement')
    logger.info(f"Found {len(spatial_elements)} spatial structure elements")
    
    spatial_info = []
    for element in spatial_elements:
        info = {
            'GlobalId': element.GlobalId,
            'Name': element.Name,
            'Description': element.Description,
            'Type': element.is_a(),
            'LongName': getattr(element, 'LongName', None),
            'CompositionType': getattr(element, 'CompositionType', None)
        }
        spatial_info.append(info)
        logger.info(f"Spatial element: {info['Type']} - {info['Name']}")
    
    # Save spatial hierarchy
    if spatial_info:
        df_spatial = pd.DataFrame(spatial_info)
        output_file = output_path / f"{ifc_file_path.stem}_spatial_hierarchy.csv"
        df_spatial.to_csv(output_file, index=False)
        logger.info(f"Saved spatial hierarchy to {output_file.name}")
else:
    logger.warning("No IFC model loaded")

# Extract all element types and count them
if ifc_model:
    all_elements = ifc_model.by_type('IfcElement')
    logger.info(f"Found {len(all_elements)} elements")
    
    # Count elements by type
    element_counts = defaultdict(int)
    for element in all_elements:
        element_type = element.is_a()
        element_counts[element_type] += 1
    
    logger.info("Element type distribution:")
    for element_type, count in sorted(element_counts.items()):
        logger.info(f"  {element_type}: {count}")
    
    # Save element type counts
    df_counts = pd.DataFrame([
        {'ElementType': elem_type, 'Count': count}
        for elem_type, count in element_counts.items()
    ])
    output_file = output_path / f"{ifc_file_path.stem}_element_counts.csv"
    df_counts.to_csv(output_file, index=False)
    logger.info(f"Saved element counts to {output_file.name}")
else:
    logger.warning("No IFC model loaded")

# Extract detailed information for all elements
if ifc_model:
    logger.info("Extracting detailed element information")
    
    element_details = []
    for element in all_elements:
        try:
            # Get placement information
            placement = None
            if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:
                try:
                    placement_matrix = ifcopenshell.util.placement.get_local_placement(element.ObjectPlacement)
                    placement = {
                        'x': float(placement_matrix[0][3]),
                        'y': float(placement_matrix[1][3]),
                        'z': float(placement_matrix[2][3])
                    }
                except:
                    placement = None
            
            info = {
                'GlobalId': element.GlobalId,
                'Name': element.Name,
                'Description': element.Description,
                'Type': element.is_a(),
                'Tag': getattr(element, 'Tag', None),
                'X': placement['x'] if placement else None,
                'Y': placement['y'] if placement else None,
                'Z': placement['z'] if placement else None
            }
            element_details.append(info)
            
        except Exception as e:
            logger.warning(f"Error processing element {element.GlobalId}: {e}")
    
    logger.info(f"Extracted details for {len(element_details)} elements")
    
    # Save detailed element information
    if element_details:
        df_elements = pd.DataFrame(element_details)
        output_file = output_path / f"{ifc_file_path.stem}_elements_detailed.csv"
        df_elements.to_csv(output_file, index=False)
        logger.info(f"Saved detailed element information to {output_file.name}")
else:
    logger.warning("No IFC model loaded")

# Extract property set information
if ifc_model:
    property_sets = ifc_model.by_type('IfcPropertySet')
    logger.info(f"Found {len(property_sets)} property sets")

    # Only log a few to prevent output overload
    max_log = 10
    pset_info = []

    for i, pset in enumerate(property_sets):
        info = {
            'GlobalId': pset.GlobalId,
            'Name': pset.Name,
            'Description': pset.Description,
            'PropertyCount': len(pset.HasProperties or [])
        }
        pset_info.append(info)

        if i < max_log:
            logger.debug(f"[{i}] {info['Name']} - {info['PropertyCount']} props")

    if len(property_sets) > max_log:
        logger.info(f"...skipped logging {len(property_sets) - max_log} more sets")

    if pset_info:
        df_psets = pd.DataFrame(pset_info)
        output_file = output_path / f"{ifc_file_path.stem}_property_sets.csv"
        df_psets.to_csv(output_file, index=False)
        logger.info(f"Saved property sets to: {output_file.name}")
else:
    logger.warning("No IFC model loaded")


# Extract material information
if ifc_model:
    materials = ifc_model.by_type('IfcMaterial')
    logger.info(f"Found {len(materials)} materials")

    max_log = 10
    material_info = []

    for i, material in enumerate(materials):
        info = {
            'Name': material.Name,
            'Description': material.Description,
            'Category': getattr(material, 'Category', None)
        }
        material_info.append(info)

        if i < max_log:
            logger.debug(f"[{i}] Material: {info['Name']}")
    
    if len(materials) > max_log:
        logger.info(f"...skipped logging {len(materials) - max_log} more materials")

    # Save material information
    if material_info:
        df_materials = pd.DataFrame(material_info)
        output_file = output_path / f"{ifc_file_path.stem}_materials.csv"
        df_materials.to_csv(output_file, index=False)
        logger.info(f"Saved materials to: {output_file.name}")
else:
    logger.warning("No IFC model loaded")


# Extract geometric representation information
if ifc_model:
    representations = ifc_model.by_type('IfcProductDefinitionShape')
    logger.info(f"Found {len(representations)} product definition shapes")

    geom_info = []
    max_items = 10

    for i, rep in enumerate(representations):
        if rep.Representations:
            for shape_rep in rep.Representations:
                info = {
                    'RepresentationIdentifier': shape_rep.RepresentationIdentifier,
                    'RepresentationType': shape_rep.RepresentationType,
                    'ContextType': getattr(shape_rep.ContextOfItems, 'ContextType', None),
                    'ItemCount': len(shape_rep.Items or [])
                }
                geom_info.append(info)

                if i < max_items:
                    logger.debug(f"[{i}] RepID: {info['RepresentationIdentifier']} | Type: {info['RepresentationType']}")

    logger.info(f"Extracted {len(geom_info)} geometric representations")

    if geom_info:
        df_geom = pd.DataFrame(geom_info)
        output_file = output_path / f"{ifc_file_path.stem}_geometric_representations.csv"
        df_geom.to_csv(output_file, index=False)
        logger.info(f"Saved geometric representations to: {output_file.name}")
else:
    logger.warning("No IFC model loaded")


if ifc_model:
    logger.info("IFC File Summary:")

    # Cached query to avoid repeated lookups
    type_counts = {}
    types_to_check = [
        'IfcProject', 'IfcSite', 'IfcBuilding', 'IfcBuildingStorey', 'IfcSpace',
        'IfcElement', 'IfcPropertySet', 'IfcMaterial'
    ]
    
    for t in types_to_check:
        type_counts[t] = len(ifc_model.by_type(t))
    
    # Avoid full list() for performance — use generator sum
    total_entities = sum(1 for _ in ifc_model)

    logger.info(f"  Schema: {ifc_model.schema}")
    logger.info(f"  Total entities: {total_entities}")
    for t, count in type_counts.items():
        logger.info(f"  {t.replace('Ifc', ''):>18}: {count}")
    
    summary_data = {
        'Metric': ['Schema', 'Total_Entities'] + [t for t in type_counts],
        'Value': [ifc_model.schema, total_entities] + [type_counts[t] for t in type_counts]
    }

    df_summary = pd.DataFrame(summary_data)
    output_file = output_path / f"{ifc_file_path.stem}_summary.csv"
    df_summary.to_csv(output_file, index=False)
    logger.info(f"Saved summary to: {output_file.name}")
else:
    logger.warning("No IFC model loaded")

# List all generated CSV files
csv_files = list(output_path.glob("*.csv"))
logger.info(f"Generated metadata files ({len(csv_files)}):")

for csv_file in sorted(csv_files):
    file_size_kb = csv_file.stat().st_size / 1024
    logger.info(f"  - {csv_file.name} ({file_size_kb:.1f} KB)")