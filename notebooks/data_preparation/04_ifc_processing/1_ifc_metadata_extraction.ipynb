{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC Metadata Extraction\n", "\n", "This notebook extracts metadata from IFC (Industry Foundation Classes) files step by step.\n", "\n", "Features:\n", "1. Load and inspect IFC file structure\n", "2. Extract project information\n", "3. Extract spatial hierarchy\n", "4. Extract element types and properties\n", "5. Extract geometric information\n", "6. Export metadata to CSV files\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** July 2025"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Dependencies"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import logging\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "from collections import defaultdict\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:14,176 - INFO - ifcopenshell is available\n"]}], "source": ["# Check for ifcopenshell\n", "try:\n", "    import ifcopenshell\n", "    import ifcopenshell.util.element\n", "    import ifcopenshell.util.placement\n", "    IFC_SUPPORT = True\n", "    logger.info(\"ifcopenshell is available\")\n", "except ImportError:\n", "    IFC_SUPPORT = False\n", "    logger.error(\"ifcopens<PERSON> is not installed\")\n", "    logger.info(\"Install with: pip install ifcopenshell\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration and File Paths"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GRE.EEC.S.00.IT.P.14353.00.265.ifc \u001b[34mmodello\u001b[m\u001b[m\n", "Trino_Fly_2.obj\n"]}], "source": ["!ls ../../../data/raw/trino_enel/ifc/"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:14,448 - INFO - Base path: ../../../data/raw/trino_enel/ifc\n", "2025-07-02 15:44:14,448 - INFO - Output path: ../../../data/processed/trino_enel/ifc_metadata\n"]}], "source": ["# Set up paths\n", "base_path = Path(\"../../../data/raw/trino_enel/ifc\")\n", "output_path = Path(\"../../../data/processed/trino_enel/ifc_metadata\")\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Base path: {base_path}\")\n", "logger.info(f\"Output path: {output_path}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:14,453 - INFO - Found 1 IFC files\n", "2025-07-02 15:44:14,454 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265.ifc\n"]}], "source": ["# Find IFC files\n", "ifc_files = list(base_path.glob(\"*.ifc\"))\n", "logger.info(f\"Found {len(ifc_files)} IFC files\")\n", "for ifc_file in ifc_files:\n", "    logger.info(f\"  - {ifc_file.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Load IFC File"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:14,459 - INFO - Loading IFC file: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "2025-07-02 15:44:21,732 - INFO - Successfully loaded IFC file\n", "2025-07-02 15:44:21,733 - INFO - IFC schema: IFC4\n"]}], "source": ["# Load first IFC file\n", "if not IFC_SUPPORT:\n", "    logger.error(\"Cannot proceed without ifcopens<PERSON>\")\n", "    ifc_model = None\n", "elif not ifc_files:\n", "    logger.error(\"No IFC files found\")\n", "    ifc_model = None\n", "else:\n", "    ifc_file_path = ifc_files[0]\n", "    logger.info(f\"Loading IFC file: {ifc_file_path.name}\")\n", "    \n", "    try:\n", "        ifc_model = ifcopenshell.open(str(ifc_file_path))\n", "        logger.info(f\"Successfully loaded IFC file\")\n", "        logger.info(f\"IFC schema: {ifc_model.schema}\")\n", "    except Exception as e:\n", "        logger.error(f\"Failed to load IFC file: {e}\")\n", "        ifc_model = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Extract Project Information"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:21,739 - INFO - Found 1 projects\n", "2025-07-02 15:44:21,739 - INFO - Project: Project Number - None\n", "2025-07-02 15:44:21,747 - INFO - Saved project information to GRE.EEC.S.00.IT.P.14353.00.265_projects.csv\n"]}], "source": ["# Extract project information\n", "if ifc_model:\n", "    projects = ifc_model.by_type('IfcProject')\n", "    logger.info(f\"Found {len(projects)} projects\")\n", "    \n", "    project_info = []\n", "    for project in projects:\n", "        info = {\n", "            'GlobalId': project.GlobalId,\n", "            'Name': project.Name,\n", "            'Description': project.Description,\n", "            'Phase': project.Phase,\n", "            'LongName': getattr(project, 'LongName', None)\n", "        }\n", "        project_info.append(info)\n", "        logger.info(f\"Project: {info['Name']} - {info['Description']}\")\n", "    \n", "    # Save project information\n", "    if project_info:\n", "        df_projects = pd.DataFrame(project_info)\n", "        output_file = output_path / f\"{ifc_file_path.stem}_projects.csv\"\n", "        df_projects.to_csv(output_file, index=False)\n", "        logger.info(f\"Saved project information to {output_file.name}\")\n", "else:\n", "    logger.warning(\"No IFC model loaded\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Extract Spatial Hierarchy"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:21,752 - INFO - Found 4 spatial structure elements\n", "2025-07-02 15:44:21,752 - INFO - Spatial element: IfcBuilding - \n", "2025-07-02 15:44:21,753 - INFO - Spatial element: IfcBuildingStorey - Level 0\n", "2025-07-02 15:44:21,753 - INFO - Spatial element: IfcBuildingStorey - Level 1\n", "2025-07-02 15:44:21,753 - INFO - Spatial element: IfcSite - Surface:840048\n", "2025-07-02 15:44:21,754 - INFO - Saved spatial hierarchy to GRE.EEC.S.00.IT.P.14353.00.265_spatial_hierarchy.csv\n"]}], "source": ["# Extract spatial hierarchy\n", "if ifc_model:\n", "    spatial_elements = ifc_model.by_type('IfcSpatialStructureElement')\n", "    logger.info(f\"Found {len(spatial_elements)} spatial structure elements\")\n", "    \n", "    spatial_info = []\n", "    for element in spatial_elements:\n", "        info = {\n", "            'GlobalId': element.GlobalId,\n", "            'Name': element.Name,\n", "            'Description': element.Description,\n", "            'Type': element.is_a(),\n", "            'LongName': getattr(element, 'LongName', None),\n", "            'CompositionType': getattr(element, 'CompositionType', None)\n", "        }\n", "        spatial_info.append(info)\n", "        logger.info(f\"Spatial element: {info['Type']} - {info['Name']}\")\n", "    \n", "    # Save spatial hierarchy\n", "    if spatial_info:\n", "        df_spatial = pd.DataFrame(spatial_info)\n", "        output_file = output_path / f\"{ifc_file_path.stem}_spatial_hierarchy.csv\"\n", "        df_spatial.to_csv(output_file, index=False)\n", "        logger.info(f\"Saved spatial hierarchy to {output_file.name}\")\n", "else:\n", "    logger.warning(\"No IFC model loaded\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Extract Element Types"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:21,803 - INFO - Found 40605 elements\n", "2025-07-02 15:44:21,823 - INFO - Element type distribution:\n", "2025-07-02 15:44:21,823 - INFO -   IfcBuildingElementProxy: 1\n", "2025-07-02 15:44:21,823 - INFO -   IfcCableCarrierFitting: 6700\n", "2025-07-02 15:44:21,824 - INFO -   IfcCableCarrierSegment: 7115\n", "2025-07-02 15:44:21,824 - INFO -   IfcColumn: 14460\n", "2025-07-02 15:44:21,825 - INFO -   IfcElectricDistributionBoard: 404\n", "2025-07-02 15:44:21,825 - INFO -   IfcElectricMotor: 6192\n", "2025-07-02 15:44:21,825 - INFO -   IfcSolarDevice: 5682\n", "2025-07-02 15:44:21,825 - INFO -   IfcTransformer: 51\n", "2025-07-02 15:44:21,827 - INFO - Saved element counts to GRE.EEC.S.00.IT.P.14353.00.265_element_counts.csv\n"]}], "source": ["# Extract all element types and count them\n", "if ifc_model:\n", "    all_elements = ifc_model.by_type('IfcElement')\n", "    logger.info(f\"Found {len(all_elements)} elements\")\n", "    \n", "    # Count elements by type\n", "    element_counts = defaultdict(int)\n", "    for element in all_elements:\n", "        element_type = element.is_a()\n", "        element_counts[element_type] += 1\n", "    \n", "    logger.info(\"Element type distribution:\")\n", "    for element_type, count in sorted(element_counts.items()):\n", "        logger.info(f\"  {element_type}: {count}\")\n", "    \n", "    # Save element type counts\n", "    df_counts = pd.DataFrame([\n", "        {'ElementType': elem_type, 'Count': count}\n", "        for elem_type, count in element_counts.items()\n", "    ])\n", "    output_file = output_path / f\"{ifc_file_path.stem}_element_counts.csv\"\n", "    df_counts.to_csv(output_file, index=False)\n", "    logger.info(f\"Saved element counts to {output_file.name}\")\n", "else:\n", "    logger.warning(\"No IFC model loaded\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Extract Detailed Element Information"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:21,833 - INFO - Extracting detailed element information\n", "2025-07-02 15:44:28,505 - INFO - Extracted details for 40605 elements\n", "2025-07-02 15:44:28,651 - INFO - Saved detailed element information to GRE.EEC.S.00.IT.P.14353.00.265_elements_detailed.csv\n"]}], "source": ["# Extract detailed information for all elements\n", "if ifc_model:\n", "    logger.info(\"Extracting detailed element information\")\n", "    \n", "    element_details = []\n", "    for element in all_elements:\n", "        try:\n", "            # Get placement information\n", "            placement = None\n", "            if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:\n", "                try:\n", "                    placement_matrix = ifcopenshell.util.placement.get_local_placement(element.ObjectPlacement)\n", "                    placement = {\n", "                        'x': float(placement_matrix[0][3]),\n", "                        'y': float(placement_matrix[1][3]),\n", "                        'z': float(placement_matrix[2][3])\n", "                    }\n", "                except:\n", "                    placement = None\n", "            \n", "            info = {\n", "                'GlobalId': element.GlobalId,\n", "                'Name': element.Name,\n", "                'Description': element.Description,\n", "                'Type': element.is_a(),\n", "                'Tag': getattr(element, 'Tag', None),\n", "                'X': placement['x'] if placement else None,\n", "                'Y': placement['y'] if placement else None,\n", "                'Z': placement['z'] if placement else None\n", "            }\n", "            element_details.append(info)\n", "            \n", "        except Exception as e:\n", "            logger.warning(f\"Error processing element {element.GlobalId}: {e}\")\n", "    \n", "    logger.info(f\"Extracted details for {len(element_details)} elements\")\n", "    \n", "    # Save detailed element information\n", "    if element_details:\n", "        df_elements = pd.DataFrame(element_details)\n", "        output_file = output_path / f\"{ifc_file_path.stem}_elements_detailed.csv\"\n", "        df_elements.to_csv(output_file, index=False)\n", "        logger.info(f\"Saved detailed element information to {output_file.name}\")\n", "else:\n", "    logger.warning(\"No IFC model loaded\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Extract Properties and Property Sets"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:28,988 - INFO - Found 279465 property sets\n", "2025-07-02 15:44:31,747 - INFO - ...skipped logging 279455 more sets\n", "2025-07-02 15:44:32,106 - INFO - Saved property sets to: GRE.EEC.S.00.IT.P.14353.00.265_property_sets.csv\n"]}], "source": ["# Extract property set information\n", "if ifc_model:\n", "    property_sets = ifc_model.by_type('IfcPropertySet')\n", "    logger.info(f\"Found {len(property_sets)} property sets\")\n", "\n", "    # Only log a few to prevent output overload\n", "    max_log = 10\n", "    pset_info = []\n", "\n", "    for i, pset in enumerate(property_sets):\n", "        info = {\n", "            'GlobalId': pset.GlobalId,\n", "            'Name': pset.Name,\n", "            'Description': pset.Description,\n", "            'PropertyCount': len(pset.HasProperties or [])\n", "        }\n", "        pset_info.append(info)\n", "\n", "        if i < max_log:\n", "            logger.debug(f\"[{i}] {info['Name']} - {info['PropertyCount']} props\")\n", "\n", "    if len(property_sets) > max_log:\n", "        logger.info(f\"...skipped logging {len(property_sets) - max_log} more sets\")\n", "\n", "    if pset_info:\n", "        df_psets = pd.DataFrame(pset_info)\n", "        output_file = output_path / f\"{ifc_file_path.stem}_property_sets.csv\"\n", "        df_psets.to_csv(output_file, index=False)\n", "        logger.info(f\"Saved property sets to: {output_file.name}\")\n", "else:\n", "    logger.warning(\"No IFC model loaded\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Extract Material Information"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:32,112 - INFO - Found 8 materials\n", "2025-07-02 15:44:32,115 - INFO - Saved materials to: GRE.EEC.S.00.IT.P.14353.00.265_materials.csv\n"]}], "source": ["# Extract material information\n", "if ifc_model:\n", "    materials = ifc_model.by_type('IfcMaterial')\n", "    logger.info(f\"Found {len(materials)} materials\")\n", "\n", "    max_log = 10\n", "    material_info = []\n", "\n", "    for i, material in enumerate(materials):\n", "        info = {\n", "            'Name': material.Name,\n", "            'Description': material.Description,\n", "            'Category': getattr(material, 'Category', None)\n", "        }\n", "        material_info.append(info)\n", "\n", "        if i < max_log:\n", "            logger.debug(f\"[{i}] Material: {info['Name']}\")\n", "    \n", "    if len(materials) > max_log:\n", "        logger.info(f\"...skipped logging {len(materials) - max_log} more materials\")\n", "\n", "    # Save material information\n", "    if material_info:\n", "        df_materials = pd.DataFrame(material_info)\n", "        output_file = output_path / f\"{ifc_file_path.stem}_materials.csv\"\n", "        df_materials.to_csv(output_file, index=False)\n", "        logger.info(f\"Saved materials to: {output_file.name}\")\n", "else:\n", "    logger.warning(\"No IFC model loaded\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Extract Geometric Representations"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:32,139 - INFO - Found 40606 product definition shapes\n", "2025-07-02 15:44:32,860 - INFO - Extracted 40606 geometric representations\n", "2025-07-02 15:44:32,906 - INFO - Saved geometric representations to: GRE.EEC.S.00.IT.P.14353.00.265_geometric_representations.csv\n"]}], "source": ["# Extract geometric representation information\n", "if ifc_model:\n", "    representations = ifc_model.by_type('IfcProductDefinitionShape')\n", "    logger.info(f\"Found {len(representations)} product definition shapes\")\n", "\n", "    geom_info = []\n", "    max_items = 10\n", "\n", "    for i, rep in enumerate(representations):\n", "        if rep.Representations:\n", "            for shape_rep in rep.Representations:\n", "                info = {\n", "                    'RepresentationIdentifier': shape_rep.RepresentationIdentifier,\n", "                    'RepresentationType': shape_rep.RepresentationType,\n", "                    'ContextType': getattr(shape_rep.ContextOfItems, 'ContextType', None),\n", "                    'ItemCount': len(shape_rep.Items or [])\n", "                }\n", "                geom_info.append(info)\n", "\n", "                if i < max_items:\n", "                    logger.debug(f\"[{i}] RepID: {info['RepresentationIdentifier']} | Type: {info['RepresentationType']}\")\n", "\n", "    logger.info(f\"Extracted {len(geom_info)} geometric representations\")\n", "\n", "    if geom_info:\n", "        df_geom = pd.DataFrame(geom_info)\n", "        output_file = output_path / f\"{ifc_file_path.stem}_geometric_representations.csv\"\n", "        df_geom.to_csv(output_file, index=False)\n", "        logger.info(f\"Saved geometric representations to: {output_file.name}\")\n", "else:\n", "    logger.warning(\"No IFC model loaded\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Summary and Statistics"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:32,911 - INFO - IFC File Summary:\n", "2025-07-02 15:44:35,478 - INFO -   Schema: IFC4\n", "2025-07-02 15:44:35,479 - INFO -   Total entities: 2212652\n", "2025-07-02 15:44:35,479 - INFO -              Project: 1\n", "2025-07-02 15:44:35,479 - INFO -                 Site: 1\n", "2025-07-02 15:44:35,480 - INFO -             Building: 1\n", "2025-07-02 15:44:35,480 - INFO -       BuildingStorey: 2\n", "2025-07-02 15:44:35,480 - INFO -                Space: 0\n", "2025-07-02 15:44:35,481 - INFO -              Element: 40605\n", "2025-07-02 15:44:35,481 - INFO -          PropertySet: 279465\n", "2025-07-02 15:44:35,481 - INFO -             Material: 8\n", "2025-07-02 15:44:35,483 - INFO - Saved summary to: GRE.EEC.S.00.IT.P.14353.00.265_summary.csv\n"]}], "source": ["if ifc_model:\n", "    logger.info(\"IFC File Summary:\")\n", "\n", "    # Cached query to avoid repeated lookups\n", "    type_counts = {}\n", "    types_to_check = [\n", "        'IfcProject', 'IfcSite', 'IfcBuilding', 'IfcBuildingStorey', 'IfcSpace',\n", "        'IfcElement', 'IfcPropertySet', 'IfcMaterial'\n", "    ]\n", "    \n", "    for t in types_to_check:\n", "        type_counts[t] = len(ifc_model.by_type(t))\n", "    \n", "    # Avoid full list() for performance — use generator sum\n", "    total_entities = sum(1 for _ in ifc_model)\n", "\n", "    logger.info(f\"  Schema: {ifc_model.schema}\")\n", "    logger.info(f\"  Total entities: {total_entities}\")\n", "    for t, count in type_counts.items():\n", "        logger.info(f\"  {t.replace('Ifc', ''):>18}: {count}\")\n", "    \n", "    summary_data = {\n", "        'Metric': ['Schema', 'Total_Entities'] + [t for t in type_counts],\n", "        'Value': [ifc_model.schema, total_entities] + [type_counts[t] for t in type_counts]\n", "    }\n", "\n", "    df_summary = pd.DataFrame(summary_data)\n", "    output_file = output_path / f\"{ifc_file_path.stem}_summary.csv\"\n", "    df_summary.to_csv(output_file, index=False)\n", "    logger.info(f\"Saved summary to: {output_file.name}\")\n", "else:\n", "    logger.warning(\"No IFC model loaded\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. List Generated Files"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 15:44:35,488 - INFO - Generated metadata files (8):\n", "2025-07-02 15:44:35,489 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_element_counts.csv (0.2 KB)\n", "2025-07-02 15:44:35,489 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_elements_detailed.csv (5734.3 KB)\n", "2025-07-02 15:44:35,490 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_geometric_representations.csv (1278.8 KB)\n", "2025-07-02 15:44:35,490 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_materials.csv (0.2 KB)\n", "2025-07-02 15:44:35,490 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_projects.csv (0.1 KB)\n", "2025-07-02 15:44:35,491 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_property_sets.csv (14564.4 KB)\n", "2025-07-02 15:44:35,491 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_spatial_hierarchy.csv (0.3 KB)\n", "2025-07-02 15:44:35,491 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_summary.csv (0.2 KB)\n"]}], "source": ["# List all generated CSV files\n", "csv_files = list(output_path.glob(\"*.csv\"))\n", "logger.info(f\"Generated metadata files ({len(csv_files)}):\")\n", "\n", "for csv_file in sorted(csv_files):\n", "    file_size_kb = csv_file.stat().st_size / 1024\n", "    logger.info(f\"  - {csv_file.name} ({file_size_kb:.1f} KB)\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GRE.EEC.S.00.IT.P.14353.00.265_element_counts.csv\n", "GRE.EEC.S.00.IT.P.14353.00.265_elements_detailed.csv\n", "GRE.EEC.S.00.IT.P.14353.00.265_geometric_representations.csv\n", "GRE.EEC.S.00.IT.P.14353.00.265_materials.csv\n", "GRE.EEC.S.00.IT.P.14353.00.265_projects.csv\n", "GRE.EEC.S.00.IT.P.14353.00.265_property_sets.csv\n", "GRE.EEC.S.00.IT.P.14353.00.265_spatial_hierarchy.csv\n", "GRE.EEC.S.00.IT.P.14353.00.265_summary.csv\n"]}], "source": ["!ls ../../../data/processed/trino_enel/ifc_metadata"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}