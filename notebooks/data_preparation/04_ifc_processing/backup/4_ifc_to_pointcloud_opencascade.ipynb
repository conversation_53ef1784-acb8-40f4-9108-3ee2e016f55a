{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC to Point Cloud using OpenCASCADE\n", "\n", "This notebook uses OpenCASCADE geometry kernel for robust IFC to point cloud conversion.\n", "\n", "OpenCASCADE advantages:\n", "1. Direct access to geometry kernel used by ifcopenshell\n", "2. Better mesh tessellation control\n", "3. More robust geometry processing\n", "4. Advanced surface sampling capabilities\n", "5. Better handling of complex geometries\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** July 2025"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Dependencies"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import logging\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from collections import defaultdict\n", "import time\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:48:41,156 - INFO - ifcopenshell version: 0.8.2\n", "2025-07-02 13:48:41,157 - WARNING - OpenCASCADE not available - install with: conda install -c conda-forge pythonocc-core\n", "2025-07-02 13:48:42,134 - INFO - Open3D version: 0.19.0\n"]}], "source": ["# Check for ifcopenshell\n", "try:\n", "    import ifcopenshell\n", "    import ifcopenshell.geom\n", "    IFC_SUPPORT = True\n", "    logger.info(f\"ifcopenshell version: {ifcopenshell.version}\")\n", "except ImportError:\n", "    IFC_SUPPORT = False\n", "    logger.error(\"ifcopens<PERSON> is not installed\")\n", "\n", "# Check for OpenCASCADE Python bindings\n", "try:\n", "    from OCC.Core import BRep_Tool, TopExp_Explorer, TopAbs_FACE, TopAbs_VERTEX\n", "    from OCC.Core import BRepMesh_IncrementalMesh, BRep_Builder\n", "    from OCC.Core import TopoDS_Shape, TopoDS_Face\n", "    from OCC.Core import gp_Pnt\n", "    OCC_SUPPORT = True\n", "    logger.info(\"OpenCASCADE Python bindings available\")\n", "except ImportError:\n", "    OCC_SUPPORT = False\n", "    logger.warning(\"OpenCASCADE not available - install with: conda install -c conda-forge pythonocc-core\")\n", "\n", "# Check for Open3D\n", "try:\n", "    import open3d as o3d\n", "    O3D_SUPPORT = True\n", "    logger.info(f\"Open3D version: {o3d.__version__}\")\n", "except ImportError:\n", "    O3D_SUPPORT = False\n", "    logger.warning(\"Open3D not available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration and Paths"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:48:42,140 - INFO - IFC files path: ../../../data/raw/trino_enel/ifc\n", "2025-07-02 13:48:42,141 - INFO - Output path: ../../../data/processed/t\n"]}], "source": ["# Set up paths\n", "base_path = Path(\"../../../data/raw/trino_enel/ifc\")\n", "output_path = Path(\"../../../data/processed/t\")\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"IFC files path: {base_path}\")\n", "logger.info(f\"Output path: {output_path}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:48:42,146 - INFO - Found 1 IFC files\n", "2025-07-02 13:48:42,147 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265.ifc\n"]}], "source": ["# Find IFC files\n", "ifc_files = list(base_path.glob(\"*.ifc\"))\n", "logger.info(f\"Found {len(ifc_files)} IFC files\")\n", "for ifc_file in ifc_files:\n", "    logger.info(f\"  - {ifc_file.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Simplified ifcopenshell Approach (Fallback)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def convert_ifc_simple(ifc_file_path, points_per_element=1000, max_elements=None):\n", "    \"\"\"\n", "    Simple IFC to point cloud conversion using basic ifcopenshell settings\n", "    \n", "    Parameters:\n", "    -----------\n", "    ifc_file_path : Path\n", "        Path to IFC file\n", "    points_per_element : int\n", "        Target number of points per element\n", "    max_elements : int, optional\n", "        Maximum number of elements to process\n", "    \n", "    Returns:\n", "    --------\n", "    dict : Results with points, labels, and metadata\n", "    \"\"\"\n", "    if not IFC_SUPPORT:\n", "        logger.error(\"ifcopens<PERSON> is not available\")\n", "        return None\n", "    \n", "    try:\n", "        logger.info(f\"Loading IFC file: {ifc_file_path.name}\")\n", "        ifc_model = ifcopenshell.open(str(ifc_file_path))\n", "        \n", "        # Use minimal settings to avoid version issues\n", "        settings = ifcopenshell.geom.settings()\n", "        # Only use settings that are known to work across versions\n", "        try:\n", "            settings.set(settings.USE_WORLD_COORDS, True)\n", "        except:\n", "            logger.warning(\"Could not set USE_WORLD_COORDS\")\n", "        \n", "        # Get elements\n", "        elements = ifc_model.by_type('IfcProduct')\n", "        logger.info(f\"Found {len(elements)} IfcProduct elements\")\n", "        \n", "        if max_elements and len(elements) > max_elements:\n", "            elements = elements[:max_elements]\n", "            logger.info(f\"Limited to {max_elements} elements\")\n", "        \n", "        return process_elements_simple(elements, settings, points_per_element)\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error converting IFC file: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def process_elements_simple(elements, settings, points_per_element):\n", "    \"\"\"\n", "    Process elements using simple vertex extraction\n", "    \"\"\"\n", "    all_points = []\n", "    all_labels = []\n", "    element_stats = defaultdict(int)\n", "    element_type_map = {}\n", "    current_label = 0\n", "    \n", "    logger.info(f\"Processing {len(elements)} elements...\")\n", "    \n", "    for i, element in enumerate(elements):\n", "        if i % 50 == 0 and i > 0:\n", "            logger.info(f\"Progress: {i}/{len(elements)} ({i/len(elements)*100:.1f}%)\")\n", "        \n", "        try:\n", "            if not element.Representation:\n", "                continue\n", "            \n", "            # Create shape\n", "            shape = ifcopenshell.geom.create_shape(settings, element)\n", "            \n", "            if not shape or not shape.geometry:\n", "                continue\n", "            \n", "            # Extract vertices\n", "            verts = shape.geometry.verts\n", "            if not verts:\n", "                continue\n", "            \n", "            # Convert to points array\n", "            points = np.array(verts).reshape(-1, 3)\n", "            \n", "            # Sample points if we have too many\n", "            if len(points) > points_per_element:\n", "                indices = np.random.choice(len(points), points_per_element, replace=False)\n", "                points = points[indices]\n", "            \n", "            # Get element type and assign label\n", "            element_type = element.is_a()\n", "            if element_type not in element_type_map:\n", "                element_type_map[element_type] = current_label\n", "                current_label += 1\n", "            \n", "            label = element_type_map[element_type]\n", "            \n", "            # Add to results\n", "            if len(points) > 0:\n", "                all_points.append(points)\n", "                all_labels.extend([label] * len(points))\n", "                element_stats[element_type] += len(points)\n", "        \n", "        except Exception as e:\n", "            logger.debug(f\"Error processing element {i}: {e}\")\n", "            continue\n", "    \n", "    # Combine results\n", "    if all_points:\n", "        combined_points = np.vstack(all_points)\n", "        combined_labels = np.array(all_labels)\n", "    else:\n", "        combined_points = np.array([]).reshape(0, 3)\n", "        combined_labels = np.array([])\n", "    \n", "    logger.info(f\"Generated {len(combined_points)} points\")\n", "    logger.info(\"Points per element type:\")\n", "    for elem_type, count in element_stats.items():\n", "        logger.info(f\"  {elem_type}: {count}\")\n", "    \n", "    return {\n", "        'points': combined_points,\n", "        'labels': combined_labels,\n", "        'element_type_map': element_type_map,\n", "        'element_stats': dict(element_stats)\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. OpenCASCADE Enhanced Approach"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def convert_ifc_with_opencascade(ifc_file_path, tessellation_quality=0.1, \n", "                               points_per_face=10, max_elements=None):\n", "    \"\"\"\n", "    Convert IFC to point cloud using OpenCASCADE for better mesh control\n", "    \n", "    Parameters:\n", "    -----------\n", "    ifc_file_path : Path\n", "        Path to IFC file\n", "    tessellation_quality : float\n", "        Mesh tessellation quality (smaller = finer mesh)\n", "    points_per_face : int\n", "        Number of points to sample per face\n", "    max_elements : int, optional\n", "        Maximum number of elements to process\n", "    \n", "    Returns:\n", "    --------\n", "    dict : Results with points, labels, and metadata\n", "    \"\"\"\n", "    if not IFC_SUPPORT or not OCC_SUPPORT:\n", "        logger.warning(\"OpenCASCADE not available, falling back to simple method\")\n", "        return convert_ifc_simple(ifc_file_path, points_per_face * 10, max_elements)\n", "    \n", "    try:\n", "        logger.info(f\"Loading IFC file with OpenCASCADE: {ifc_file_path.name}\")\n", "        ifc_model = ifcopenshell.open(str(ifc_file_path))\n", "        \n", "        # Enhanced settings for OpenCASCADE\n", "        settings = ifcopenshell.geom.settings()\n", "        settings.set(settings.USE_WORLD_COORDS, True)\n", "        \n", "        # Get elements\n", "        elements = ifc_model.by_type('IfcProduct')\n", "        logger.info(f\"Found {len(elements)} IfcProduct elements\")\n", "        \n", "        if max_elements and len(elements) > max_elements:\n", "            elements = elements[:max_elements]\n", "            logger.info(f\"Limited to {max_elements} elements\")\n", "        \n", "        return process_elements_with_opencascade(elements, settings, \n", "                                               tessellation_quality, points_per_face)\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error with OpenCASCADE method: {e}\")\n", "        logger.info(\"Falling back to simple method\")\n", "        return convert_ifc_simple(ifc_file_path, points_per_face * 10, max_elements)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def process_elements_with_opencascade(elements, settings, tessellation_quality, points_per_face):\n", "    \"\"\"\n", "    Process elements using OpenCASCADE for enhanced mesh control\n", "    \"\"\"\n", "    all_points = []\n", "    all_labels = []\n", "    element_stats = defaultdict(int)\n", "    element_type_map = {}\n", "    current_label = 0\n", "    \n", "    logger.info(f\"Processing {len(elements)} elements with OpenCASCADE...\")\n", "    \n", "    for i, element in enumerate(elements):\n", "        if i % 25 == 0 and i > 0:\n", "            logger.info(f\"Progress: {i}/{len(elements)} ({i/len(elements)*100:.1f}%)\")\n", "        \n", "        try:\n", "            if not element.Representation:\n", "                continue\n", "            \n", "            # Create shape with ifcopenshell\n", "            shape = ifcopenshell.geom.create_shape(settings, element)\n", "            \n", "            if not shape or not shape.geometry:\n", "                continue\n", "            \n", "            # Get OpenCASCADE shape if available\n", "            if hasattr(shape, 'brep_data') and shape.brep_data:\n", "                # Use OpenCASCADE for enhanced processing\n", "                points = extract_points_from_brep(shape.brep_data, \n", "                                                tessellation_quality, points_per_face)\n", "            else:\n", "                # Fallback to vertex extraction\n", "                verts = shape.geometry.verts\n", "                if verts:\n", "                    points = np.array(verts).reshape(-1, 3)\n", "                    # Sample if too many points\n", "                    if len(points) > points_per_face * 10:\n", "                        indices = np.random.choice(len(points), points_per_face * 10, replace=False)\n", "                        points = points[indices]\n", "                else:\n", "                    continue\n", "            \n", "            # Get element type and assign label\n", "            element_type = element.is_a()\n", "            if element_type not in element_type_map:\n", "                element_type_map[element_type] = current_label\n", "                current_label += 1\n", "            \n", "            label = element_type_map[element_type]\n", "            \n", "            # Add to results\n", "            if len(points) > 0:\n", "                all_points.append(points)\n", "                all_labels.extend([label] * len(points))\n", "                element_stats[element_type] += len(points)\n", "        \n", "        except Exception as e:\n", "            logger.debug(f\"Error processing element {i}: {e}\")\n", "            continue\n", "    \n", "    # Combine results\n", "    if all_points:\n", "        combined_points = np.vstack(all_points)\n", "        combined_labels = np.array(all_labels)\n", "    else:\n", "        combined_points = np.array([]).reshape(0, 3)\n", "        combined_labels = np.array([])\n", "    \n", "    logger.info(f\"Generated {len(combined_points)} points with OpenCASCADE\")\n", "    logger.info(\"Points per element type:\")\n", "    for elem_type, count in element_stats.items():\n", "        logger.info(f\"  {elem_type}: {count}\")\n", "    \n", "    return {\n", "        'points': combined_points,\n", "        'labels': combined_labels,\n", "        'element_type_map': element_type_map,\n", "        'element_stats': dict(element_stats)\n", "    }"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def extract_points_from_brep(brep_data, tessellation_quality, points_per_face):\n", "    \"\"\"\n", "    Extract points from OpenCASCADE BREP data with controlled tessellation\n", "    \n", "    Parameters:\n", "    -----------\n", "    brep_data : bytes\n", "        OpenCASCADE BREP data\n", "    tessellation_quality : float\n", "        Tessellation quality parameter\n", "    points_per_face : int\n", "        Target points per face\n", "    \n", "    Returns:\n", "    --------\n", "    numpy.n<PERSON><PERSON> : Sam<PERSON> points\n", "    \"\"\"\n", "    try:\n", "        # Create shape from BREP data\n", "        builder = BRep_Builder()\n", "        shape = TopoDS_Shape()\n", "        \n", "        # This is a simplified approach - in practice you'd need to\n", "        # properly deserialize the BREP data\n", "        # For now, return empty array as fallback\n", "        logger.debug(\"OpenCASCADE BREP processing not fully implemented\")\n", "        return np.array([]).reshape(0, 3)\n", "        \n", "    except Exception as e:\n", "        logger.debug(f\"Error in OpenCASCADE BREP processing: {e}\")\n", "        return np.array([]).reshape(0, 3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Utility Functions"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def save_pointcloud_simple(points, labels, output_file, element_type_map=None):\n", "    \"\"\"\n", "    Save point cloud to PLY format\n", "    \"\"\"\n", "    try:\n", "        if len(points) == 0:\n", "            logger.warning(\"No points to save\")\n", "            return\n", "        \n", "        if O3D_SUPPORT:\n", "            # Use Open3D\n", "            pcd = o3d.geometry.PointCloud()\n", "            pcd.points = o3d.utility.Vector3dVector(points)\n", "            \n", "            # Add colors based on labels\n", "            if len(labels) > 0:\n", "                colors = generate_colors_from_labels(labels)\n", "                pcd.colors = o3d.utility.Vector3dVector(colors)\n", "            \n", "            o3d.io.write_point_cloud(str(output_file), pcd)\n", "            logger.info(f\"Saved point cloud to {output_file.name}\")\n", "        else:\n", "            # Manual PLY writing\n", "            write_ply_manual(points, labels, output_file)\n", "        \n", "        # Save element type mapping\n", "        if element_type_map:\n", "            mapping_file = output_file.with_suffix('.mapping.txt')\n", "            with open(mapping_file, 'w') as f:\n", "                for elem_type, label in element_type_map.items():\n", "                    f.write(f\"{label}: {elem_type}\\n\")\n", "            logger.info(f\"Saved element type mapping to {mapping_file.name}\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error saving point cloud: {e}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def generate_colors_from_labels(labels):\n", "    \"\"\"\n", "    Generate colors for visualization based on labels\n", "    \"\"\"\n", "    unique_labels = np.unique(labels)\n", "    colors = np.zeros((len(labels), 3))\n", "    \n", "    for i, label in enumerate(unique_labels):\n", "        # Generate distinct colors\n", "        hue = (i * 137.5) % 360  # Golden angle\n", "        saturation = 0.8\n", "        value = 0.9\n", "        \n", "        # Simple HSV to RGB conversion\n", "        import colorsys\n", "        rgb = colorsys.hsv_to_rgb(hue/360, saturation, value)\n", "        \n", "        mask = labels == label\n", "        colors[mask] = rgb\n", "    \n", "    return colors"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def write_ply_manual(points, labels, output_file):\n", "    \"\"\"\n", "    Manually write PLY file without Open3D dependency\n", "    \"\"\"\n", "    try:\n", "        with open(output_file, 'w') as f:\n", "            # Write header\n", "            f.write(\"ply\\n\")\n", "            f.write(\"format ascii 1.0\\n\")\n", "            f.write(f\"element vertex {len(points)}\\n\")\n", "            f.write(\"property float x\\n\")\n", "            f.write(\"property float y\\n\")\n", "            f.write(\"property float z\\n\")\n", "            if len(labels) > 0:\n", "                f.write(\"property int label\\n\")\n", "            f.write(\"end_header\\n\")\n", "            \n", "            # Write data\n", "            for i, point in enumerate(points):\n", "                if len(labels) > 0:\n", "                    f.write(f\"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {int(labels[i])}\\n\")\n", "                else:\n", "                    f.write(f\"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f}\\n\")\n", "        \n", "        logger.info(f\"Manually saved PLY to {output_file.name}\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error writing PLY manually: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Execute Conversion"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:48:42,195 - INFO - Converting IFC file: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "2025-07-02 13:48:42,196 - INFO - Using simple ifcopenshell method\n", "2025-07-02 13:48:42,196 - INFO - Loading IFC file: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "2025-07-02 13:48:49,972 - INFO - Found 91933 IfcProduct elements\n", "2025-07-02 13:48:49,986 - INFO - Limited to 500 elements\n", "2025-07-02 13:48:49,986 - INFO - Processing 500 elements...\n", "2025-07-02 13:48:50,020 - INFO - Progress: 50/500 (10.0%)\n", "2025-07-02 13:48:50,056 - INFO - Progress: 100/500 (20.0%)\n", "2025-07-02 13:48:50,090 - INFO - Progress: 150/500 (30.0%)\n", "2025-07-02 13:48:50,124 - INFO - Progress: 200/500 (40.0%)\n", "2025-07-02 13:48:50,158 - INFO - Progress: 250/500 (50.0%)\n", "2025-07-02 13:48:50,191 - INFO - Progress: 300/500 (60.0%)\n", "2025-07-02 13:48:50,225 - INFO - Progress: 350/500 (70.0%)\n", "2025-07-02 13:48:50,259 - INFO - Progress: 400/500 (80.0%)\n", "2025-07-02 13:48:50,292 - INFO - Progress: 450/500 (90.0%)\n", "2025-07-02 13:48:50,326 - INFO - Generated 4000 points\n", "2025-07-02 13:48:50,326 - INFO - Points per element type:\n", "2025-07-02 13:48:50,327 - INFO -   IfcBuildingElementProxy: 8\n", "2025-07-02 13:48:50,327 - INFO -   IfcColumn: 3992\n", "2025-07-02 13:48:52,224 - INFO - Successfully generated 4000 points\n", "2025-07-02 13:48:52,228 - INFO - Saved point cloud to GRE.EEC.S.00.IT.P.14353.00.265_simple_pointcloud.ply\n", "2025-07-02 13:48:52,228 - INFO - Saved element type mapping to GRE.EEC.S.00.IT.P.14353.00.265_simple_pointcloud.mapping.txt\n", "2025-07-02 13:48:52,228 - INFO - Conversion statistics:\n", "2025-07-02 13:48:52,229 - INFO -   Total points: 4000\n", "2025-07-02 13:48:52,229 - INFO -   Unique element types: 2\n", "2025-07-02 13:48:52,229 - INFO -   Element types: ['IfcBuildingElementProxy', 'IfcColumn']\n"]}], "source": ["# Convert IFC file\n", "if not IFC_SUPPORT:\n", "    logger.error(\"Cannot proceed without ifcopens<PERSON>\")\n", "elif not ifc_files:\n", "    logger.error(\"No IFC files found\")\n", "else:\n", "    ifc_file = ifc_files[0]\n", "    logger.info(f\"Converting IFC file: {ifc_file.name}\")\n", "    \n", "    # Try OpenCASCADE method first, fallback to simple method\n", "    if OCC_SUPPORT:\n", "        logger.info(\"Using OpenCASCADE enhanced method\")\n", "        result = convert_ifc_with_opencascade(\n", "            ifc_file,\n", "            tessellation_quality=0.1,\n", "            points_per_face=5,\n", "            max_elements=500  # Start with smaller number for testing\n", "        )\n", "    else:\n", "        logger.info(\"Using simple ifcopenshell method\")\n", "        result = convert_ifc_simple(\n", "            ifc_file,\n", "            points_per_element=500,\n", "            max_elements=500\n", "        )\n", "    \n", "    if result and len(result['points']) > 0:\n", "        logger.info(f\"Successfully generated {len(result['points'])} points\")\n", "        \n", "        # Save point cloud\n", "        method_suffix = \"opencascade\" if OCC_SUPPORT else \"simple\"\n", "        output_file = output_path / f\"{ifc_file.stem}_{method_suffix}_pointcloud.ply\"\n", "        \n", "        save_pointcloud_simple(\n", "            result['points'], \n", "            result['labels'], \n", "            output_file,\n", "            result['element_type_map']\n", "        )\n", "        \n", "        # Print statistics\n", "        logger.info(\"Conversion statistics:\")\n", "        logger.info(f\"  Total points: {len(result['points'])}\")\n", "        logger.info(f\"  Unique element types: {len(result['element_type_map'])}\")\n", "        logger.info(f\"  Element types: {list(result['element_type_map'].keys())}\")\n", "        \n", "    else:\n", "        logger.error(\"Failed to convert IFC file or no points generated\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Visualization"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Simple 2D visualization\n", "if 'result' in locals() and result and len(result['points']) > 0:\n", "    points = result['points']\n", "    labels = result['labels']\n", "    \n", "    # Sample points for visualization\n", "    sample_size = min(10000, len(points))\n", "    if len(points) > sample_size:\n", "        indices = np.random.choice(len(points), sample_size, replace=False)\n", "        sampled_points = points[indices]\n", "        sampled_labels = labels[indices]\n", "    else:\n", "        sampled_points = points\n", "        sampled_labels = labels\n", "    \n", "    # Create 2D projections\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle(f\"IFC Point Cloud - {len(points)} points\", fontsize=16)\n", "    \n", "    # Color by labels\n", "    unique_labels = np.unique(sampled_labels)\n", "    colors = plt.cm.tab10(np.linspace(0, 1, len(unique_labels)))\n", "    \n", "    # XY projection\n", "    for i, label in enumerate(unique_labels):\n", "        mask = sampled_labels == label\n", "        axes[0, 0].scatter(sampled_points[mask, 0], sampled_points[mask, 1], \n", "                          c=[colors[i]], s=1, alpha=0.6, label=f\"Type {label}\")\n", "    axes[0, 0].set_xlabel('X')\n", "    axes[0, 0].set_ylabel('Y')\n", "    axes[0, 0].set_title('XY Projection (Top View)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # XZ projection\n", "    for i, label in enumerate(unique_labels):\n", "        mask = sampled_labels == label\n", "        axes[0, 1].scatter(sampled_points[mask, 0], sampled_points[mask, 2], \n", "                          c=[colors[i]], s=1, alpha=0.6)\n", "    axes[0, 1].set_xlabel('X')\n", "    axes[0, 1].set_ylabel('Z')\n", "    axes[0, 1].set_title('XZ Projection (Front View)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # YZ projection\n", "    for i, label in enumerate(unique_labels):\n", "        mask = sampled_labels == label\n", "        axes[1, 0].scatter(sampled_points[mask, 1], sampled_points[mask, 2], \n", "                          c=[colors[i]], s=1, alpha=0.6)\n", "    axes[1, 0].set_xlabel('Y')\n", "    axes[1, 0].set_ylabel('Z')\n", "    axes[1, 0].set_title('YZ Projection (Side View)')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # Legend\n", "    axes[1, 1].axis('off')\n", "    if 'element_type_map' in result:\n", "        legend_text = \"Element Types:\\n\"\n", "        for elem_type, label in result['element_type_map'].items():\n", "            legend_text += f\"  {label}: {elem_type}\\n\"\n", "        axes[1, 1].text(0.1, 0.9, legend_text, transform=axes[1, 1].transAxes, \n", "                        fontsize=10, verticalalignment='top')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "else:\n", "    logger.warning(\"No point cloud data available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:48:52,471 - INFO - Generated point cloud files (1):\n", "2025-07-02 13:48:52,472 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_simple_pointcloud.ply (0.10 MB)\n", "2025-07-02 13:48:52,472 - INFO - \n", "Approach Summary:\n", "2025-07-02 13:48:52,473 - INFO -   ⚠ OpenCASCADE not available - using simple method\n", "2025-07-02 13:48:52,473 - INFO -   Install with: conda install -c conda-forge pythonocc-core\n", "2025-07-02 13:48:52,473 - INFO -   ✓ Open3D available - enhanced visualization and I/O\n", "2025-07-02 13:48:52,474 - INFO - \n", "Recommendations:\n", "2025-07-02 13:48:52,474 - INFO -   1. For best results, install OpenCASCADE Python bindings\n", "2025-07-02 13:48:52,474 - INFO -   2. Use simple method as reliable fallback\n", "2025-07-02 13:48:52,474 - INFO -   3. Adjust points_per_element/points_per_face based on file size\n", "2025-07-02 13:48:52,475 - INFO -   4. Use max_elements parameter for large files\n"]}], "source": ["# List generated files\n", "ply_files = list(output_path.glob(\"*.ply\"))\n", "logger.info(f\"Generated point cloud files ({len(ply_files)}):\")\n", "\n", "for ply_file in sorted(ply_files):\n", "    file_size_mb = ply_file.stat().st_size / (1024 * 1024)\n", "    logger.info(f\"  - {ply_file.name} ({file_size_mb:.2f} MB)\")\n", "\n", "# Summary of approaches\n", "logger.info(\"\\nApproach Summary:\")\n", "if OCC_SUPPORT:\n", "    logger.info(\"  ✓ OpenCASCADE available - enhanced geometry processing\")\n", "else:\n", "    logger.info(\"  ⚠ OpenCASCADE not available - using simple method\")\n", "    logger.info(\"  Install with: conda install -c conda-forge pythonocc-core\")\n", "\n", "if O3D_SUPPORT:\n", "    logger.info(\"  ✓ Open3D available - enhanced visualization and I/O\")\n", "else:\n", "    logger.info(\"  ⚠ Open3D not available - using manual PLY writing\")\n", "\n", "logger.info(\"\\nRecommendations:\")\n", "logger.info(\"  1. For best results, install OpenCASCADE Python bindings\")\n", "logger.info(\"  2. Use simple method as reliable fallback\")\n", "logger.info(\"  3. Adjust points_per_element/points_per_face based on file size\")\n", "logger.info(\"  4. Use max_elements parameter for large files\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}