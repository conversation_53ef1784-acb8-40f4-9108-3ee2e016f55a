{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Robust IFC to Point Cloud Converter\n", "\n", "This notebook provides a robust solution for converting IFC files to point clouds using ifcopenshell with advanced mesh extraction and face sampling.\n", "\n", "Features:\n", "1. Direct mesh extraction from IFC geometry\n", "2. Face-based point sampling with barycentric coordinates\n", "3. Multiple output formats (PLY, OBJ, PCD)\n", "4. Element type filtering and classification\n", "5. Robust error handling and geometry validation\n", "6. Memory-efficient processing for large files\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** July 2025"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Dependencies"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import logging\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from collections import defaultdict\n", "import time\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:40:51,368 - INFO - ifcopenshell version: 0.8.2\n", "2025-07-02 13:40:52,695 - INFO - Open3D version: 0.19.0\n", "2025-07-02 13:40:52,864 - INFO - Trimesh version: 4.6.12\n"]}], "source": ["# Check for required packages\n", "try:\n", "    import ifcopenshell\n", "    import ifcopenshell.geom\n", "    IFC_SUPPORT = True\n", "    logger.info(f\"ifcopenshell version: {ifcopenshell.version}\")\n", "except ImportError:\n", "    IFC_SUPPORT = False\n", "    logger.error(\"ifcopens<PERSON> is not installed\")\n", "\n", "try:\n", "    import open3d as o3d\n", "    O3D_SUPPORT = True\n", "    logger.info(f\"Open3D version: {o3d.__version__}\")\n", "except ImportError:\n", "    O3D_SUPPORT = False\n", "    logger.warning(\"Open3D is not installed\")\n", "\n", "try:\n", "    import trimesh\n", "    TRIMESH_SUPPORT = True\n", "    logger.info(f\"Trimesh version: {trimesh.__version__}\")\n", "except ImportError:\n", "    TRIMESH_SUPPORT = False\n", "    logger.warning(\"<PERSON><PERSON><PERSON> is not installed - install with: pip install trimesh\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration and Paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:40:52,871 - INFO - IFC files path: ../../../data/raw/ifc_files\n", "2025-07-02 13:40:52,871 - INFO - Point clouds output: ../../../data/processed/point_clouds\n", "2025-07-02 13:40:52,871 - INFO - Meshes output: ../../../data/processed/meshes\n"]}], "source": ["# Set up paths\n", "base_path = Path(\"../../../data/raw/trino_enel/ifc\")\n", "output_path = Path(\"../../../data/processed/trino_enel/ifc_pointclouds\")\n", "mesh_output_path = Path(\"../../../data/processed/meshes\")\n", "\n", "# Create output directories\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "mesh_output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"IFC files path: {base_path}\")\n", "logger.info(f\"Point clouds output: {output_path}\")\n", "logger.info(f\"Meshes output: {mesh_output_path}\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:41:20,486 - INFO - Found 0 IFC files\n"]}], "source": ["# Find IFC files\n", "ifc_files = list(base_path.glob(\"*.ifc\"))\n", "logger.info(f\"Found {len(ifc_files)} IFC files\")\n", "for ifc_file in ifc_files:\n", "    logger.info(f\"  - {ifc_file.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Mesh Extraction Functions"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def extract_mesh_from_ifc_element(element, settings):\n", "    \"\"\"\n", "    Extract mesh data from a single IFC element\n", "    \n", "    Parameters:\n", "    -----------\n", "    element : IFC element\n", "        The IFC element to process\n", "    settings : ifcopenshell.geom.settings\n", "        Geometry settings\n", "    \n", "    Returns:\n", "    --------\n", "    dict : Mesh data with vertices, faces, and metadata\n", "    \"\"\"\n", "    try:\n", "        if not element.Representation:\n", "            return None\n", "        \n", "        # Create shape from element\n", "        shape = ifcopenshell.geom.create_shape(settings, element)\n", "        \n", "        if not shape or not shape.geometry:\n", "            return None\n", "        \n", "        # Extract vertices and faces\n", "        verts = shape.geometry.verts\n", "        faces = shape.geometry.faces\n", "        \n", "        if not verts or not faces:\n", "            return None\n", "        \n", "        # Reshape vertices to (N, 3)\n", "        vertices = np.array(verts).reshape(-1, 3)\n", "        \n", "        # Reshape faces to (M, 3) - assuming triangular faces\n", "        faces_array = np.array(faces).reshape(-1, 3)\n", "        \n", "        mesh_data = {\n", "            'vertices': vertices,\n", "            'faces': faces_array,\n", "            'element_type': element.is_a(),\n", "            'element_id': element.GlobalId,\n", "            'element_name': element.Name\n", "        }\n", "        \n", "        return mesh_data\n", "        \n", "    except Exception as e:\n", "        logger.debug(f\"Error extracting mesh from element {element.GlobalId}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def sample_points_from_mesh_faces(vertices, faces, points_per_face=10):\n", "    \"\"\"\n", "    Sample points from mesh faces using barycentric coordinates\n", "    \n", "    Parameters:\n", "    -----------\n", "    vertices : numpy.ndarray\n", "        Mesh vertices (N, 3)\n", "    faces : numpy.n<PERSON><PERSON>\n", "        Mesh faces (M, 3)\n", "    points_per_face : int\n", "        Number of points to sample per face\n", "    \n", "    Returns:\n", "    --------\n", "    numpy.ndarray : Sampled points (K, 3)\n", "    \"\"\"\n", "    if len(faces) == 0:\n", "        return np.array([]).reshape(0, 3)\n", "    \n", "    sampled_points = []\n", "    \n", "    for face in faces:\n", "        # Get face vertices\n", "        v0, v1, v2 = vertices[face[0]], vertices[face[1]], vertices[face[2]]\n", "        \n", "        # Generate random barycentric coordinates\n", "        r1 = np.random.random(points_per_face)\n", "        r2 = np.random.random(points_per_face)\n", "        \n", "        # Ensure points are inside triangle\n", "        mask = r1 + r2 > 1\n", "        r1[mask] = 1 - r1[mask]\n", "        r2[mask] = 1 - r2[mask]\n", "        \n", "        # Calculate barycentric coordinates\n", "        u = r1\n", "        v = r2\n", "        w = 1 - u - v\n", "        \n", "        # Sample points using barycentric coordinates\n", "        points = (u[:, np.newaxis] * v0 + \n", "                 v[:, np.newaxis] * v1 + \n", "                 w[:, np.newaxis] * v2)\n", "        \n", "        sampled_points.append(points)\n", "    \n", "    if sampled_points:\n", "        return np.vstack(sampled_points)\n", "    else:\n", "        return np.array([]).reshape(0, 3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Main Conversion Functions"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def convert_ifc_to_pointcloud_robust(ifc_file_path, element_types=None, \n", "                                   points_per_face=5, max_elements=None,\n", "                                   save_mesh=False):\n", "    \"\"\"\n", "    Convert IFC file to point cloud using robust mesh extraction\n", "    \n", "    Parameters:\n", "    -----------\n", "    ifc_file_path : Path\n", "        Path to IFC file\n", "    element_types : list, optional\n", "        List of IFC element types to include\n", "    points_per_face : int\n", "        Number of points to sample per mesh face\n", "    max_elements : int, optional\n", "        Maximum number of elements to process\n", "    save_mesh : bool\n", "        Whether to save intermediate mesh files\n", "    \n", "    Returns:\n", "    --------\n", "    dict : Results with points, labels, and metadata\n", "    \"\"\"\n", "    if not IFC_SUPPORT:\n", "        logger.error(\"ifcopens<PERSON> is not available\")\n", "        return None\n", "    \n", "    try:\n", "        logger.info(f\"Loading IFC file: {ifc_file_path.name}\")\n", "        ifc_model = ifcopenshell.open(str(ifc_file_path))\n", "        \n", "        # Set up geometry settings\n", "        settings = ifcopenshell.geom.settings()\n", "        settings.set(settings.USE_WORLD_COORDS, True)\n", "        settings.set(settings.WELD_VERTICES, True)\n", "        settings.set(settings.USE_BREP_DATA, False)\n", "        settings.set(settings.SEW_SHELLS, True)\n", "        \n", "        # Get elements to process\n", "        if element_types:\n", "            elements = []\n", "            for element_type in element_types:\n", "                elements.extend(ifc_model.by_type(element_type))\n", "            logger.info(f\"Found {len(elements)} elements of types {element_types}\")\n", "        else:\n", "            elements = ifc_model.by_type('IfcProduct')\n", "            logger.info(f\"Found {len(elements)} IfcProduct elements\")\n", "        \n", "        # Limit elements if specified\n", "        if max_elements and len(elements) > max_elements:\n", "            elements = elements[:max_elements]\n", "            logger.info(f\"Limited to {max_elements} elements\")\n", "        \n", "        return process_elements_to_pointcloud(elements, settings, ifc_file_path,\n", "                                            points_per_face, save_mesh)\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error converting IFC file: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def process_elements_to_pointcloud(elements, settings, ifc_file_path, \n", "                                 points_per_face, save_mesh):\n", "    \"\"\"\n", "    Process IFC elements and convert to point cloud\n", "    \"\"\"\n", "    all_points = []\n", "    all_labels = []\n", "    element_stats = defaultdict(int)\n", "    \n", "    # Element type to label mapping\n", "    element_type_map = {}\n", "    current_label = 0\n", "    \n", "    logger.info(f\"Processing {len(elements)} elements...\")\n", "    start_time = time.time()\n", "    \n", "    for i, element in enumerate(elements):\n", "        if i % 100 == 0 and i > 0:\n", "            elapsed = time.time() - start_time\n", "            rate = i / elapsed\n", "            eta = (len(elements) - i) / rate if rate > 0 else 0\n", "            logger.info(f\"Progress: {i}/{len(elements)} ({i/len(elements)*100:.1f}%) - ETA: {eta:.1f}s\")\n", "        \n", "        # Extract mesh from element\n", "        mesh_data = extract_mesh_from_ifc_element(element, settings)\n", "        \n", "        if mesh_data is None:\n", "            continue\n", "        \n", "        element_type = mesh_data['element_type']\n", "        \n", "        # Assign label to element type\n", "        if element_type not in element_type_map:\n", "            element_type_map[element_type] = current_label\n", "            current_label += 1\n", "        \n", "        label = element_type_map[element_type]\n", "        \n", "        # Sample points from mesh faces\n", "        points = sample_points_from_mesh_faces(\n", "            mesh_data['vertices'], \n", "            mesh_data['faces'], \n", "            points_per_face\n", "        )\n", "        \n", "        if len(points) > 0:\n", "            all_points.append(points)\n", "            all_labels.extend([label] * len(points))\n", "            element_stats[element_type] += len(points)\n", "        \n", "        # Save mesh if requested\n", "        if save_mesh and TRIMESH_SUPPORT and len(mesh_data['vertices']) > 0:\n", "            save_element_mesh(mesh_data, ifc_file_path, i)\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    if all_points:\n", "        combined_points = np.vstack(all_points)\n", "        combined_labels = np.array(all_labels)\n", "    else:\n", "        combined_points = np.array([]).reshape(0, 3)\n", "        combined_labels = np.array([])\n", "    \n", "    logger.info(f\"Generated {len(combined_points)} points from {len(elements)} elements\")\n", "    logger.info(\"Points per element type:\")\n", "    for elem_type, count in element_stats.items():\n", "        logger.info(f\"  {elem_type}: {count}\")\n", "    \n", "    return {\n", "        'points': combined_points,\n", "        'labels': combined_labels,\n", "        'element_type_map': element_type_map,\n", "        'element_stats': dict(element_stats)\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Mesh and Point Cloud Saving Functions"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["def save_element_mesh(mesh_data, ifc_file_path, element_index):\n", "    \"\"\"\n", "    Save individual element mesh to file\n", "    \"\"\"\n", "    try:\n", "        if not TRIMESH_SUPPORT:\n", "            return\n", "        \n", "        mesh = trimesh.<PERSON>(\n", "            vertices=mesh_data['vertices'],\n", "            faces=mesh_data['faces']\n", "        )\n", "        \n", "        # Create filename\n", "        element_type = mesh_data['element_type']\n", "        filename = f\"{ifc_file_path.stem}_{element_type}_{element_index:04d}.obj\"\n", "        output_file = mesh_output_path / filename\n", "        \n", "        # Export mesh\n", "        mesh.export(str(output_file))\n", "        \n", "    except Exception as e:\n", "        logger.debug(f\"Error saving mesh for element {element_index}: {e}\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def save_pointcloud_with_labels(points, labels, output_file, element_type_map=None):\n", "    \"\"\"\n", "    Save point cloud with class labels to PLY file\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point coordinates (N, 3)\n", "    labels : numpy.ndarray\n", "        Point labels (N,)\n", "    output_file : Path\n", "        Output file path\n", "    element_type_map : dict, optional\n", "        Mapping from element types to labels\n", "    \"\"\"\n", "    try:\n", "        if len(points) == 0:\n", "            logger.warning(\"No points to save\")\n", "            return\n", "        \n", "        if O3D_SUPPORT:\n", "            # Use Open3D for standard PLY format\n", "            pcd = o3d.geometry.PointCloud()\n", "            pcd.points = o3d.utility.Vector3dVector(points)\n", "            \n", "            # Add colors based on labels\n", "            if len(labels) > 0:\n", "                colors = generate_colors_from_labels(labels)\n", "                pcd.colors = o3d.utility.Vector3dVector(colors)\n", "            \n", "            o3d.io.write_point_cloud(str(output_file), pcd)\n", "            logger.info(f\"Saved point cloud to {output_file.name}\")\n", "        \n", "        # Also save custom PLY with labels\n", "        if len(labels) > 0:\n", "            custom_ply_file = output_file.with_suffix('.labeled.ply')\n", "            save_ply_with_labels(points, labels, custom_ply_file)\n", "        \n", "        # Save element type mapping\n", "        if element_type_map:\n", "            mapping_file = output_file.with_suffix('.mapping.txt')\n", "            with open(mapping_file, 'w') as f:\n", "                for elem_type, label in element_type_map.items():\n", "                    f.write(f\"{label}: {elem_type}\\n\")\n", "            logger.info(f\"Saved element type mapping to {mapping_file.name}\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error saving point cloud: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Utility Functions"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def generate_colors_from_labels(labels):\n", "    \"\"\"\n", "    Generate colors for point cloud based on labels\n", "    \"\"\"\n", "    unique_labels = np.unique(labels)\n", "    colors = np.zeros((len(labels), 3))\n", "    \n", "    # Generate distinct colors for each label\n", "    for i, label in enumerate(unique_labels):\n", "        # Use HSV color space for better distinction\n", "        hue = (i * 137.5) % 360  # Golden angle for good distribution\n", "        saturation = 0.7\n", "        value = 0.9\n", "        \n", "        # Convert HSV to RGB\n", "        import colorsys\n", "        rgb = colorsys.hsv_to_rgb(hue/360, saturation, value)\n", "        \n", "        mask = labels == label\n", "        colors[mask] = rgb\n", "    \n", "    return colors"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["def save_ply_with_labels(points, labels, output_file):\n", "    \"\"\"\n", "    Save PLY file with custom format including labels\n", "    \"\"\"\n", "    try:\n", "        with open(output_file, 'w') as f:\n", "            # Write PLY header\n", "            f.write(\"ply\\n\")\n", "            f.write(\"format ascii 1.0\\n\")\n", "            f.write(f\"element vertex {len(points)}\\n\")\n", "            f.write(\"property float x\\n\")\n", "            f.write(\"property float y\\n\")\n", "            f.write(\"property float z\\n\")\n", "            f.write(\"property int label\\n\")\n", "            f.write(\"end_header\\n\")\n", "            \n", "            # Write vertex data\n", "            for point, label in zip(points, labels):\n", "                f.write(f\"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {int(label)}\\n\")\n", "        \n", "        logger.info(f\"Saved labeled PLY to {output_file.name}\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error saving labeled PLY: {e}\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["def visualize_pointcloud_3d(points, labels=None, title=\"Point Cloud\", sample_size=20000):\n", "    \"\"\"\n", "    Visualize point cloud with Open3D\n", "    \"\"\"\n", "    if not O3D_SUPPORT or len(points) == 0:\n", "        logger.warning(\"Cannot visualize - Open3D not available or no points\")\n", "        return\n", "    \n", "    # Sample points if too many\n", "    if len(points) > sample_size:\n", "        indices = np.random.choice(len(points), sample_size, replace=False)\n", "        sampled_points = points[indices]\n", "        sampled_labels = labels[indices] if labels is not None else None\n", "    else:\n", "        sampled_points = points\n", "        sampled_labels = labels\n", "    \n", "    # Create point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(sampled_points)\n", "    \n", "    # Add colors based on labels\n", "    if sampled_labels is not None:\n", "        colors = generate_colors_from_labels(sampled_labels)\n", "        pcd.colors = o3d.utility.Vector3dVector(colors)\n", "    \n", "    # Visualize\n", "    o3d.visualization.draw_geometries([pcd], window_name=title)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Execute Conversion"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:41:49,180 - ERROR - No IFC files found\n"]}], "source": ["# Convert first IFC file if available\n", "if not IFC_SUPPORT:\n", "    logger.error(\"Cannot proceed without ifcopens<PERSON>\")\n", "elif not ifc_files:\n", "    logger.error(\"No IFC files found\")\n", "else:\n", "    ifc_file = ifc_files[0]\n", "    logger.info(f\"Converting IFC file: {ifc_file.name}\")\n", "    \n", "    # Convert with robust method\n", "    result = convert_ifc_to_pointcloud_robust(\n", "        ifc_file,\n", "        element_types=None,  # Include all element types\n", "        points_per_face=3,   # 3 points per face for good coverage\n", "        max_elements=1000,   # Limit for testing\n", "        save_mesh=False      # Set to True to save intermediate meshes\n", "    )\n", "    \n", "    if result and len(result['points']) > 0:\n", "        logger.info(f\"Successfully generated {len(result['points'])} points\")\n", "        \n", "        # Save point cloud\n", "        output_file = output_path / f\"{ifc_file.stem}_robust_pointcloud.ply\"\n", "        save_pointcloud_with_labels(\n", "            result['points'], \n", "            result['labels'], \n", "            output_file,\n", "            result['element_type_map']\n", "        )\n", "        \n", "    else:\n", "        logger.error(\"Failed to convert IFC file or no points generated\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Visualization"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:41:51,992 - WARNING - No point cloud data available for visualization\n"]}], "source": ["# Visualize the generated point cloud\n", "if 'result' in locals() and result and len(result['points']) > 0:\n", "    logger.info(\"Visualizing point cloud...\")\n", "    \n", "    # 3D visualization with Open3D\n", "    visualize_pointcloud_3d(\n", "        result['points'], \n", "        result['labels'], \n", "        \"Robust IFC Point Cloud\",\n", "        sample_size=15000\n", "    )\n", "    \n", "    # Print statistics\n", "    logger.info(\"Point cloud statistics:\")\n", "    logger.info(f\"  Total points: {len(result['points'])}\")\n", "    logger.info(f\"  Unique labels: {len(np.unique(result['labels']))}\")\n", "    logger.info(f\"  Element types: {list(result['element_type_map'].keys())}\")\n", "    \n", "else:\n", "    logger.warning(\"No point cloud data available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. <PERSON><PERSON> Processing"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:41:56,471 - INFO - <PERSON><PERSON> processing skipped - only one file or ifcopenshell not available\n"]}], "source": ["# Process all IFC files\n", "if IFC_SUPPORT and len(ifc_files) > 1:\n", "    logger.info(f\"Processing {len(ifc_files)} IFC files in batch mode\")\n", "    \n", "    for ifc_file in ifc_files:\n", "        output_file = output_path / f\"{ifc_file.stem}_robust_pointcloud.ply\"\n", "        \n", "        if output_file.exists():\n", "            logger.info(f\"Skipping {ifc_file.name} - output already exists\")\n", "            continue\n", "        \n", "        logger.info(f\"Processing {ifc_file.name}...\")\n", "        \n", "        result = convert_ifc_to_pointcloud_robust(\n", "            ifc_file,\n", "            element_types=None,\n", "            points_per_face=3,\n", "            max_elements=2000,  # Increase for production\n", "            save_mesh=False\n", "        )\n", "        \n", "        if result and len(result['points']) > 0:\n", "            save_pointcloud_with_labels(\n", "                result['points'], \n", "                result['labels'], \n", "                output_file,\n", "                result['element_type_map']\n", "            )\n", "            logger.info(f\"Completed {ifc_file.name} - {len(result['points'])} points\")\n", "        else:\n", "            logger.warning(f\"Failed to process {ifc_file.name}\")\n", "\n", "else:\n", "    logger.info(\"Batch processing skipped - only one file or ifcopenshell not available\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}