import os
import sys
import logging
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from collections import defaultdict
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Check for ifcopenshell
try:
    import ifcopenshell
    import ifcopenshell.geom
    IFC_SUPPORT = True
    logger.info(f"ifcopenshell version: {ifcopenshell.version}")
except ImportError:
    IFC_SUPPORT = False
    logger.error("ifcopenshell is not installed")

# Check for OpenCASCADE Python bindings
try:
    from OCC.Core import BRep_Tool, TopExp_Explorer, TopAbs_FACE, TopAbs_VERTEX
    from OCC.Core import BRepMesh_IncrementalMesh, BRep_Builder
    from OCC.Core import TopoDS_Shape, TopoDS_Face
    from OCC.Core import gp_Pnt
    OCC_SUPPORT = True
    logger.info("OpenCASCADE Python bindings available")
except ImportError:
    OCC_SUPPORT = False
    logger.warning("OpenCASCADE not available - install with: conda install -c conda-forge pythonocc-core")

# Check for Open3D
try:
    import open3d as o3d
    O3D_SUPPORT = True
    logger.info(f"Open3D version: {o3d.__version__}")
except ImportError:
    O3D_SUPPORT = False
    logger.warning("Open3D not available")

# Set up paths
base_path = Path("../../../data/raw/trino_enel/ifc")
output_path = Path("../../../data/processed/t")
output_path.mkdir(parents=True, exist_ok=True)

logger.info(f"IFC files path: {base_path}")
logger.info(f"Output path: {output_path}")

# Find IFC files
ifc_files = list(base_path.glob("*.ifc"))
logger.info(f"Found {len(ifc_files)} IFC files")
for ifc_file in ifc_files:
    logger.info(f"  - {ifc_file.name}")

def convert_ifc_simple(ifc_file_path, points_per_element=1000, max_elements=None):
    """
    Simple IFC to point cloud conversion using basic ifcopenshell settings
    
    Parameters:
    -----------
    ifc_file_path : Path
        Path to IFC file
    points_per_element : int
        Target number of points per element
    max_elements : int, optional
        Maximum number of elements to process
    
    Returns:
    --------
    dict : Results with points, labels, and metadata
    """
    if not IFC_SUPPORT:
        logger.error("ifcopenshell is not available")
        return None
    
    try:
        logger.info(f"Loading IFC file: {ifc_file_path.name}")
        ifc_model = ifcopenshell.open(str(ifc_file_path))
        
        # Use minimal settings to avoid version issues
        settings = ifcopenshell.geom.settings()
        # Only use settings that are known to work across versions
        try:
            settings.set(settings.USE_WORLD_COORDS, True)
        except:
            logger.warning("Could not set USE_WORLD_COORDS")
        
        # Get elements
        elements = ifc_model.by_type('IfcProduct')
        logger.info(f"Found {len(elements)} IfcProduct elements")
        
        if max_elements and len(elements) > max_elements:
            elements = elements[:max_elements]
            logger.info(f"Limited to {max_elements} elements")
        
        return process_elements_simple(elements, settings, points_per_element)
        
    except Exception as e:
        logger.error(f"Error converting IFC file: {e}")
        return None

def process_elements_simple(elements, settings, points_per_element):
    """
    Process elements using simple vertex extraction
    """
    all_points = []
    all_labels = []
    element_stats = defaultdict(int)
    element_type_map = {}
    current_label = 0
    
    logger.info(f"Processing {len(elements)} elements...")
    
    for i, element in enumerate(elements):
        if i % 50 == 0 and i > 0:
            logger.info(f"Progress: {i}/{len(elements)} ({i/len(elements)*100:.1f}%)")
        
        try:
            if not element.Representation:
                continue
            
            # Create shape
            shape = ifcopenshell.geom.create_shape(settings, element)
            
            if not shape or not shape.geometry:
                continue
            
            # Extract vertices
            verts = shape.geometry.verts
            if not verts:
                continue
            
            # Convert to points array
            points = np.array(verts).reshape(-1, 3)
            
            # Sample points if we have too many
            if len(points) > points_per_element:
                indices = np.random.choice(len(points), points_per_element, replace=False)
                points = points[indices]
            
            # Get element type and assign label
            element_type = element.is_a()
            if element_type not in element_type_map:
                element_type_map[element_type] = current_label
                current_label += 1
            
            label = element_type_map[element_type]
            
            # Add to results
            if len(points) > 0:
                all_points.append(points)
                all_labels.extend([label] * len(points))
                element_stats[element_type] += len(points)
        
        except Exception as e:
            logger.debug(f"Error processing element {i}: {e}")
            continue
    
    # Combine results
    if all_points:
        combined_points = np.vstack(all_points)
        combined_labels = np.array(all_labels)
    else:
        combined_points = np.array([]).reshape(0, 3)
        combined_labels = np.array([])
    
    logger.info(f"Generated {len(combined_points)} points")
    logger.info("Points per element type:")
    for elem_type, count in element_stats.items():
        logger.info(f"  {elem_type}: {count}")
    
    return {
        'points': combined_points,
        'labels': combined_labels,
        'element_type_map': element_type_map,
        'element_stats': dict(element_stats)
    }

def convert_ifc_with_opencascade(ifc_file_path, tessellation_quality=0.1, 
                               points_per_face=10, max_elements=None):
    """
    Convert IFC to point cloud using OpenCASCADE for better mesh control
    
    Parameters:
    -----------
    ifc_file_path : Path
        Path to IFC file
    tessellation_quality : float
        Mesh tessellation quality (smaller = finer mesh)
    points_per_face : int
        Number of points to sample per face
    max_elements : int, optional
        Maximum number of elements to process
    
    Returns:
    --------
    dict : Results with points, labels, and metadata
    """
    if not IFC_SUPPORT or not OCC_SUPPORT:
        logger.warning("OpenCASCADE not available, falling back to simple method")
        return convert_ifc_simple(ifc_file_path, points_per_face * 10, max_elements)
    
    try:
        logger.info(f"Loading IFC file with OpenCASCADE: {ifc_file_path.name}")
        ifc_model = ifcopenshell.open(str(ifc_file_path))
        
        # Enhanced settings for OpenCASCADE
        settings = ifcopenshell.geom.settings()
        settings.set(settings.USE_WORLD_COORDS, True)
        
        # Get elements
        elements = ifc_model.by_type('IfcProduct')
        logger.info(f"Found {len(elements)} IfcProduct elements")
        
        if max_elements and len(elements) > max_elements:
            elements = elements[:max_elements]
            logger.info(f"Limited to {max_elements} elements")
        
        return process_elements_with_opencascade(elements, settings, 
                                               tessellation_quality, points_per_face)
        
    except Exception as e:
        logger.error(f"Error with OpenCASCADE method: {e}")
        logger.info("Falling back to simple method")
        return convert_ifc_simple(ifc_file_path, points_per_face * 10, max_elements)

def process_elements_with_opencascade(elements, settings, tessellation_quality, points_per_face):
    """
    Process elements using OpenCASCADE for enhanced mesh control
    """
    all_points = []
    all_labels = []
    element_stats = defaultdict(int)
    element_type_map = {}
    current_label = 0
    
    logger.info(f"Processing {len(elements)} elements with OpenCASCADE...")
    
    for i, element in enumerate(elements):
        if i % 25 == 0 and i > 0:
            logger.info(f"Progress: {i}/{len(elements)} ({i/len(elements)*100:.1f}%)")
        
        try:
            if not element.Representation:
                continue
            
            # Create shape with ifcopenshell
            shape = ifcopenshell.geom.create_shape(settings, element)
            
            if not shape or not shape.geometry:
                continue
            
            # Get OpenCASCADE shape if available
            if hasattr(shape, 'brep_data') and shape.brep_data:
                # Use OpenCASCADE for enhanced processing
                points = extract_points_from_brep(shape.brep_data, 
                                                tessellation_quality, points_per_face)
            else:
                # Fallback to vertex extraction
                verts = shape.geometry.verts
                if verts:
                    points = np.array(verts).reshape(-1, 3)
                    # Sample if too many points
                    if len(points) > points_per_face * 10:
                        indices = np.random.choice(len(points), points_per_face * 10, replace=False)
                        points = points[indices]
                else:
                    continue
            
            # Get element type and assign label
            element_type = element.is_a()
            if element_type not in element_type_map:
                element_type_map[element_type] = current_label
                current_label += 1
            
            label = element_type_map[element_type]
            
            # Add to results
            if len(points) > 0:
                all_points.append(points)
                all_labels.extend([label] * len(points))
                element_stats[element_type] += len(points)
        
        except Exception as e:
            logger.debug(f"Error processing element {i}: {e}")
            continue
    
    # Combine results
    if all_points:
        combined_points = np.vstack(all_points)
        combined_labels = np.array(all_labels)
    else:
        combined_points = np.array([]).reshape(0, 3)
        combined_labels = np.array([])
    
    logger.info(f"Generated {len(combined_points)} points with OpenCASCADE")
    logger.info("Points per element type:")
    for elem_type, count in element_stats.items():
        logger.info(f"  {elem_type}: {count}")
    
    return {
        'points': combined_points,
        'labels': combined_labels,
        'element_type_map': element_type_map,
        'element_stats': dict(element_stats)
    }

def extract_points_from_brep(brep_data, tessellation_quality, points_per_face):
    """
    Extract points from OpenCASCADE BREP data with controlled tessellation
    
    Parameters:
    -----------
    brep_data : bytes
        OpenCASCADE BREP data
    tessellation_quality : float
        Tessellation quality parameter
    points_per_face : int
        Target points per face
    
    Returns:
    --------
    numpy.ndarray : Sampled points
    """
    try:
        # Create shape from BREP data
        builder = BRep_Builder()
        shape = TopoDS_Shape()
        
        # This is a simplified approach - in practice you'd need to
        # properly deserialize the BREP data
        # For now, return empty array as fallback
        logger.debug("OpenCASCADE BREP processing not fully implemented")
        return np.array([]).reshape(0, 3)
        
    except Exception as e:
        logger.debug(f"Error in OpenCASCADE BREP processing: {e}")
        return np.array([]).reshape(0, 3)

def save_pointcloud_simple(points, labels, output_file, element_type_map=None):
    """
    Save point cloud to PLY format
    """
    try:
        if len(points) == 0:
            logger.warning("No points to save")
            return
        
        if O3D_SUPPORT:
            # Use Open3D
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            
            # Add colors based on labels
            if len(labels) > 0:
                colors = generate_colors_from_labels(labels)
                pcd.colors = o3d.utility.Vector3dVector(colors)
            
            o3d.io.write_point_cloud(str(output_file), pcd)
            logger.info(f"Saved point cloud to {output_file.name}")
        else:
            # Manual PLY writing
            write_ply_manual(points, labels, output_file)
        
        # Save element type mapping
        if element_type_map:
            mapping_file = output_file.with_suffix('.mapping.txt')
            with open(mapping_file, 'w') as f:
                for elem_type, label in element_type_map.items():
                    f.write(f"{label}: {elem_type}\n")
            logger.info(f"Saved element type mapping to {mapping_file.name}")
        
    except Exception as e:
        logger.error(f"Error saving point cloud: {e}")

def generate_colors_from_labels(labels):
    """
    Generate colors for visualization based on labels
    """
    unique_labels = np.unique(labels)
    colors = np.zeros((len(labels), 3))
    
    for i, label in enumerate(unique_labels):
        # Generate distinct colors
        hue = (i * 137.5) % 360  # Golden angle
        saturation = 0.8
        value = 0.9
        
        # Simple HSV to RGB conversion
        import colorsys
        rgb = colorsys.hsv_to_rgb(hue/360, saturation, value)
        
        mask = labels == label
        colors[mask] = rgb
    
    return colors

def write_ply_manual(points, labels, output_file):
    """
    Manually write PLY file without Open3D dependency
    """
    try:
        with open(output_file, 'w') as f:
            # Write header
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(points)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            if len(labels) > 0:
                f.write("property int label\n")
            f.write("end_header\n")
            
            # Write data
            for i, point in enumerate(points):
                if len(labels) > 0:
                    f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {int(labels[i])}\n")
                else:
                    f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f}\n")
        
        logger.info(f"Manually saved PLY to {output_file.name}")
        
    except Exception as e:
        logger.error(f"Error writing PLY manually: {e}")

# Convert IFC file
if not IFC_SUPPORT:
    logger.error("Cannot proceed without ifcopenshell")
elif not ifc_files:
    logger.error("No IFC files found")
else:
    ifc_file = ifc_files[0]
    logger.info(f"Converting IFC file: {ifc_file.name}")
    
    # Try OpenCASCADE method first, fallback to simple method
    if OCC_SUPPORT:
        logger.info("Using OpenCASCADE enhanced method")
        result = convert_ifc_with_opencascade(
            ifc_file,
            tessellation_quality=0.1,
            points_per_face=5,
            max_elements=500  # Start with smaller number for testing
        )
    else:
        logger.info("Using simple ifcopenshell method")
        result = convert_ifc_simple(
            ifc_file,
            points_per_element=500,
            max_elements=500
        )
    
    if result and len(result['points']) > 0:
        logger.info(f"Successfully generated {len(result['points'])} points")
        
        # Save point cloud
        method_suffix = "opencascade" if OCC_SUPPORT else "simple"
        output_file = output_path / f"{ifc_file.stem}_{method_suffix}_pointcloud.ply"
        
        save_pointcloud_simple(
            result['points'], 
            result['labels'], 
            output_file,
            result['element_type_map']
        )
        
        # Print statistics
        logger.info("Conversion statistics:")
        logger.info(f"  Total points: {len(result['points'])}")
        logger.info(f"  Unique element types: {len(result['element_type_map'])}")
        logger.info(f"  Element types: {list(result['element_type_map'].keys())}")
        
    else:
        logger.error("Failed to convert IFC file or no points generated")

# Simple 2D visualization
if 'result' in locals() and result and len(result['points']) > 0:
    points = result['points']
    labels = result['labels']
    
    # Sample points for visualization
    sample_size = min(10000, len(points))
    if len(points) > sample_size:
        indices = np.random.choice(len(points), sample_size, replace=False)
        sampled_points = points[indices]
        sampled_labels = labels[indices]
    else:
        sampled_points = points
        sampled_labels = labels
    
    # Create 2D projections
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f"IFC Point Cloud - {len(points)} points", fontsize=16)
    
    # Color by labels
    unique_labels = np.unique(sampled_labels)
    colors = plt.cm.tab10(np.linspace(0, 1, len(unique_labels)))
    
    # XY projection
    for i, label in enumerate(unique_labels):
        mask = sampled_labels == label
        axes[0, 0].scatter(sampled_points[mask, 0], sampled_points[mask, 1], 
                          c=[colors[i]], s=1, alpha=0.6, label=f"Type {label}")
    axes[0, 0].set_xlabel('X')
    axes[0, 0].set_ylabel('Y')
    axes[0, 0].set_title('XY Projection (Top View)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # XZ projection
    for i, label in enumerate(unique_labels):
        mask = sampled_labels == label
        axes[0, 1].scatter(sampled_points[mask, 0], sampled_points[mask, 2], 
                          c=[colors[i]], s=1, alpha=0.6)
    axes[0, 1].set_xlabel('X')
    axes[0, 1].set_ylabel('Z')
    axes[0, 1].set_title('XZ Projection (Front View)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # YZ projection
    for i, label in enumerate(unique_labels):
        mask = sampled_labels == label
        axes[1, 0].scatter(sampled_points[mask, 1], sampled_points[mask, 2], 
                          c=[colors[i]], s=1, alpha=0.6)
    axes[1, 0].set_xlabel('Y')
    axes[1, 0].set_ylabel('Z')
    axes[1, 0].set_title('YZ Projection (Side View)')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Legend
    axes[1, 1].axis('off')
    if 'element_type_map' in result:
        legend_text = "Element Types:\n"
        for elem_type, label in result['element_type_map'].items():
            legend_text += f"  {label}: {elem_type}\n"
        axes[1, 1].text(0.1, 0.9, legend_text, transform=axes[1, 1].transAxes, 
                        fontsize=10, verticalalignment='top')
    
    plt.tight_layout()
    plt.show()
    
else:
    logger.warning("No point cloud data available for visualization")

# List generated files
ply_files = list(output_path.glob("*.ply"))
logger.info(f"Generated point cloud files ({len(ply_files)}):")

for ply_file in sorted(ply_files):
    file_size_mb = ply_file.stat().st_size / (1024 * 1024)
    logger.info(f"  - {ply_file.name} ({file_size_mb:.2f} MB)")

# Summary of approaches
logger.info("\nApproach Summary:")
if OCC_SUPPORT:
    logger.info("  ✓ OpenCASCADE available - enhanced geometry processing")
else:
    logger.info("  ⚠ OpenCASCADE not available - using simple method")
    logger.info("  Install with: conda install -c conda-forge pythonocc-core")

if O3D_SUPPORT:
    logger.info("  ✓ Open3D available - enhanced visualization and I/O")
else:
    logger.info("  ⚠ Open3D not available - using manual PLY writing")

logger.info("\nRecommendations:")
logger.info("  1. For best results, install OpenCASCADE Python bindings")
logger.info("  2. Use simple method as reliable fallback")
logger.info("  3. Adjust points_per_element/points_per_face based on file size")
logger.info("  4. Use max_elements parameter for large files")