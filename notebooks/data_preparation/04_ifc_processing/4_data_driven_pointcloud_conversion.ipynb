import os
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Check ifcopenshell
try:
    import ifcopenshell
    import ifcopenshell.geom
    logger.info(f"ifcopenshell version: {ifcopenshell.version}")
except ImportError:
    logger.error("ifcopenshell not available")
    raise

# Set up paths
base_path = Path("../../../data/raw/trino_enel/ifc")
metadata_path = Path("../../../data/processed/trino_enel/ifc_metadata")
output_path = Path("../../../data/processed/data_driven_pointclouds")
output_path.mkdir(parents=True, exist_ok=True)

logger.info(f"IFC files path: {base_path}")
logger.info(f"Metadata path: {metadata_path}")
logger.info(f"Output path: {output_path}")

def load_metadata_results():
    """
    Load and analyze metadata extraction results
    """
    metadata_files = {
        'element_counts': list(metadata_path.glob("*_element_counts.csv")),
        'elements_detailed': list(metadata_path.glob("*_elements_detailed.csv")),
        'summary': list(metadata_path.glob("*_summary.csv")),
        'spatial_hierarchy': list(metadata_path.glob("*_spatial_hierarchy.csv"))
    }
    
    logger.info("Found metadata files:")
    for file_type, files in metadata_files.items():
        logger.info(f"  {file_type}: {len(files)} files")
        for file in files:
            logger.info(f"    - {file.name}")
    
    return metadata_files

def analyze_element_types(metadata_files):
    """
    Analyze what element types are actually present in the IFC file
    """
    element_analysis = {}
    
    # Load element counts
    if metadata_files['element_counts']:
        counts_file = metadata_files['element_counts'][0]
        logger.info(f"Loading element counts from: {counts_file.name}")
        
        try:
            df_counts = pd.read_csv(counts_file)
            
            logger.info("\n=== ACTUAL ELEMENT TYPES IN YOUR IFC FILE ===")
            logger.info(f"Total element types: {len(df_counts)}")
            
            # Sort by count (most common first)
            df_sorted = df_counts.sort_values('Count', ascending=False)
            
            logger.info("\nElement distribution:")
            total_elements = df_sorted['Count'].sum()
            
            for _, row in df_sorted.iterrows():
                elem_type = row['ElementType']
                count = row['Count']
                percentage = (count / total_elements) * 100
                logger.info(f"  {elem_type}: {count} ({percentage:.1f}%)")
            
            element_analysis['counts'] = df_sorted
            element_analysis['total_elements'] = total_elements
            
        except Exception as e:
            logger.error(f"Error loading element counts: {e}")
    
    return element_analysis

def create_data_driven_strategy(element_analysis):
    """
    Create conversion strategy based on actual element data
    """
    if 'counts' not in element_analysis:
        logger.error("No element count data available")
        return None
    
    df_counts = element_analysis['counts']
    available_types = set(df_counts['ElementType'].tolist())
    
    logger.info("\n=== CREATING DATA-DRIVEN CONVERSION STRATEGY ===")
    
    # Define alignment priorities based on what drones see
    alignment_priorities = {
        'CRITICAL': ['IfcWall', 'IfcRoof', 'IfcCurtainWall'],
        'HIGH': ['IfcSlab', 'IfcBuildingElementProxy', 'IfcStair'],
        'MEDIUM': ['IfcColumn', 'IfcBeam', 'IfcRailing', 'IfcDoor', 'IfcWindow'],
        'LOW': ['IfcFooting', 'IfcPile', 'IfcReinforcingBar']
    }
    
    # Find what's actually available
    strategy = {
        'available_critical': [],
        'available_high': [],
        'available_medium': [],
        'available_low': [],
        'other_elements': [],
        'recommended_elements': [],
        'point_allocation': {}
    }
    
    # Categorize available elements
    for priority, element_types in alignment_priorities.items():
        available = [et for et in element_types if et in available_types]
        strategy[f'available_{priority.lower()}'] = available
        
        if available:
            logger.info(f"{priority} priority elements found: {available}")
        else:
            logger.warning(f"No {priority} priority elements found")
    
    # Find other elements not in our priority list
    all_priority_elements = set()
    for element_types in alignment_priorities.values():
        all_priority_elements.update(element_types)
    
    other_elements = [et for et in available_types if et not in all_priority_elements]
    strategy['other_elements'] = other_elements
    
    if other_elements:
        logger.info(f"Other elements found: {other_elements}")
    
    # Create recommended processing list
    recommended = []
    recommended.extend(strategy['available_critical'])
    recommended.extend(strategy['available_high'])
    recommended.extend(strategy['available_medium'][:3])  # Limit medium priority
    
    # Add other elements if we don't have enough
    if len(recommended) < 3:
        recommended.extend(other_elements[:3])
    
    strategy['recommended_elements'] = recommended
    
    # Allocate points based on element counts and priorities
    total_target_points = 10000
    
    for elem_type in recommended:
        # Get count for this element type
        elem_count = df_counts[df_counts['ElementType'] == elem_type]['Count'].iloc[0]
        
        # Allocate points based on priority and count
        if elem_type in strategy['available_critical']:
            points_per_element = min(200, max(50, total_target_points // (elem_count * 2)))
        elif elem_type in strategy['available_high']:
            points_per_element = min(150, max(30, total_target_points // (elem_count * 3)))
        else:
            points_per_element = min(100, max(20, total_target_points // (elem_count * 4)))
        
        strategy['point_allocation'][elem_type] = {
            'points_per_element': points_per_element,
            'element_count': elem_count,
            'total_points': points_per_element * elem_count
        }
    
    logger.info("\nRecommended conversion strategy:")
    total_estimated_points = 0
    for elem_type, allocation in strategy['point_allocation'].items():
        logger.info(f"  {elem_type}: {allocation['points_per_element']} pts/elem × {allocation['element_count']} elems = {allocation['total_points']} pts")
        total_estimated_points += allocation['total_points']
    
    logger.info(f"\nEstimated total points: {total_estimated_points:,}")
    
    return strategy

# Load and analyze metadata
metadata_files = load_metadata_results()

if not any(metadata_files.values()):
    logger.error("No metadata files found!")
    logger.info("Please run the metadata extraction notebook first (1_ifc_metadata_extraction.ipynb)")
else:
    # Analyze element types
    element_analysis = analyze_element_types(metadata_files)
    
    if element_analysis:
        # Create data-driven strategy
        strategy = create_data_driven_strategy(element_analysis)
        
        if strategy:
            logger.info("\n=== DATA-DRIVEN STRATEGY CREATED ===")
            logger.info(f"Recommended elements: {strategy['recommended_elements']}")
            logger.info(f"Total element types to process: {len(strategy['recommended_elements'])}")
        else:
            logger.error("Failed to create conversion strategy")
    else:
        logger.error("Failed to analyze element types")

def convert_with_data_driven_strategy(ifc_file_path, strategy):
    """
    Convert IFC to point cloud using the data-driven strategy
    """
    logger.info(f"\n=== CONVERTING {ifc_file_path.name} WITH DATA-DRIVEN STRATEGY ===")
    
    try:
        # Load IFC
        ifc_model = ifcopenshell.open(str(ifc_file_path))
        
        # Setup geometry settings
        settings = ifcopenshell.geom.settings()
        settings.set(settings.USE_WORLD_COORDS, True)
        
        # Process elements according to strategy
        all_points = []
        all_labels = []
        element_type_map = {}
        current_label = 0
        processing_stats = {}
        
        for elem_type in strategy['recommended_elements']:
            logger.info(f"\nProcessing {elem_type}...")
            
            # Get elements of this type
            elements = ifc_model.by_type(elem_type)
            elements = [e for e in elements if e.Representation]
            
            if not elements:
                logger.warning(f"No {elem_type} elements with geometry found")
                continue
            
            # Get allocation for this element type
            allocation = strategy['point_allocation'].get(elem_type, {
                'points_per_element': 50,
                'element_count': len(elements)
            })
            
            points_per_element = allocation['points_per_element']
            
            # Assign label
            element_type_map[elem_type] = current_label
            label = current_label
            current_label += 1
            
            # Process elements
            element_points = []
            processed_count = 0
            
            for element in elements:
                try:
                    shape = ifcopenshell.geom.create_shape(settings, element)
                    if not shape or not shape.geometry:
                        continue
                    
                    verts = shape.geometry.verts
                    if not verts:
                        continue
                    
                    # Convert to points
                    points = np.array(verts).reshape(-1, 3)
                    
                    # Sample points
                    if len(points) > points_per_element:
                        indices = np.random.choice(len(points), points_per_element, replace=False)
                        points = points[indices]
                    
                    element_points.append(points)
                    processed_count += 1
                    
                except Exception as e:
                    logger.debug(f"Error processing {elem_type} element: {e}")
                    continue
            
            # Combine points for this element type
            if element_points:
                combined_points = np.vstack(element_points)
                all_points.append(combined_points)
                all_labels.extend([label] * len(combined_points))
                
                processing_stats[elem_type] = {
                    'elements_found': len(elements),
                    'elements_processed': processed_count,
                    'points_generated': len(combined_points)
                }
                
                logger.info(f"  {elem_type}: {processed_count}/{len(elements)} elements → {len(combined_points)} points")
            else:
                logger.warning(f"  {elem_type}: No points generated")
        
        # Combine all results
        if all_points:
            final_points = np.vstack(all_points)
            final_labels = np.array(all_labels)
        else:
            final_points = np.array([]).reshape(0, 3)
            final_labels = np.array([])
        
        logger.info(f"\nConversion complete: {len(final_points):,} total points")
        
        return {
            'points': final_points,
            'labels': final_labels,
            'element_type_map': element_type_map,
            'processing_stats': processing_stats,
            'strategy_used': strategy
        }
        
    except Exception as e:
        logger.error(f"Error in data-driven conversion: {e}")
        return None

def save_data_driven_results(result, output_file, ifc_file_name):
    """
    Save results with comprehensive metadata
    """
    try:
        points = result['points']
        labels = result['labels']
        
        if len(points) == 0:
            logger.warning("No points to save")
            return
        
        # Save PLY file
        with open(output_file, 'w') as f:
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(points)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property int label\n")
            f.write("end_header\n")
            
            for point, label in zip(points, labels):
                f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {int(label)}\n")
        
        logger.info(f"Saved point cloud to {output_file.name}")
        
        # Save detailed report
        report_file = output_file.with_suffix('.report.txt')
        with open(report_file, 'w') as f:
            f.write(f"DATA-DRIVEN IFC TO POINT CLOUD CONVERSION REPORT\n")
            f.write(f"={'='*60}\n\n")
            f.write(f"Source IFC File: {ifc_file_name}\n")
            f.write(f"Conversion Date: {pd.Timestamp.now()}\n")
            f.write(f"Total Points Generated: {len(points):,}\n\n")
            
            f.write(f"COORDINATE BOUNDS:\n")
            f.write(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}] (span: {points[:, 0].max() - points[:, 0].min():.1f}m)\n")
            f.write(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}] (span: {points[:, 1].max() - points[:, 1].min():.1f}m)\n")
            f.write(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}] (span: {points[:, 2].max() - points[:, 2].min():.1f}m)\n\n")
            
            f.write(f"PROCESSING STATISTICS:\n")
            for elem_type, stats in result['processing_stats'].items():
                f.write(f"  {elem_type}:\n")
                f.write(f"    Elements found: {stats['elements_found']}\n")
                f.write(f"    Elements processed: {stats['elements_processed']}\n")
                f.write(f"    Points generated: {stats['points_generated']:,}\n")
                success_rate = (stats['elements_processed'] / stats['elements_found']) * 100
                f.write(f"    Success rate: {success_rate:.1f}%\n\n")
            
            f.write(f"ELEMENT TYPE MAPPING:\n")
            for elem_type, label in result['element_type_map'].items():
                f.write(f"  {label}: {elem_type}\n")
            
            f.write(f"\nSTRATEGY USED:\n")
            f.write(f"  Recommended elements: {result['strategy_used']['recommended_elements']}\n")
            f.write(f"  Critical elements found: {result['strategy_used']['available_critical']}\n")
            f.write(f"  High priority elements found: {result['strategy_used']['available_high']}\n")
        
        logger.info(f"Saved detailed report to {report_file.name}")
        
        # Save strategy as JSON
        strategy_file = output_file.with_suffix('.strategy.json')
        with open(strategy_file, 'w') as f:
            # Convert numpy types to regular Python types for JSON serialization
            strategy_copy = result['strategy_used'].copy()
            json.dump(strategy_copy, f, indent=2, default=str)
        
        logger.info(f"Saved strategy to {strategy_file.name}")
        
    except Exception as e:
        logger.error(f"Error saving results: {e}")

# Execute the data-driven conversion
if 'strategy' in locals() and strategy:
    # Find IFC file
    ifc_files = list(base_path.glob("*.ifc"))
    
    if not ifc_files:
        logger.error("No IFC files found")
    else:
        ifc_file = ifc_files[0]
        logger.info(f"Converting {ifc_file.name} using data-driven strategy")
        
        # Convert using the strategy
        result = convert_with_data_driven_strategy(ifc_file, strategy)
        
        if result and len(result['points']) > 0:
            logger.info("\n=== DATA-DRIVEN CONVERSION RESULTS ===")
            logger.info(f"Total points: {len(result['points']):,}")
            logger.info(f"Element types processed: {len(result['element_type_map'])}")
            
            # Show processing success rates
            logger.info("\nProcessing success rates:")
            for elem_type, stats in result['processing_stats'].items():
                success_rate = (stats['elements_processed'] / stats['elements_found']) * 100
                logger.info(f"  {elem_type}: {stats['elements_processed']}/{stats['elements_found']} ({success_rate:.1f}%) → {stats['points_generated']:,} points")
            
            # Alignment assessment
            logger.info("\n=== ALIGNMENT READINESS ASSESSMENT ===")
            
            # Check for critical elements
            critical_found = any(elem in result['element_type_map'] for elem in ['IfcWall', 'IfcRoof', 'IfcCurtainWall'])
            high_priority_found = any(elem in result['element_type_map'] for elem in ['IfcSlab', 'IfcBuildingElementProxy'])
            
            if critical_found:
                logger.info("✅ Critical elements (walls/roofs) found - excellent for alignment")
            else:
                logger.warning("⚠️  No critical elements (walls/roofs) found - may affect alignment quality")
            
            if high_priority_found:
                logger.info("✅ High priority elements found - good coverage")
            
            if len(result['points']) >= 5000:
                logger.info("✅ Sufficient point density for alignment")
            else:
                logger.warning(f"⚠️  Low point count ({len(result['points'])}) - may need more elements")
            
            # Save results
            output_file = output_path / f"{ifc_file.stem}_data_driven.ply"
            save_data_driven_results(result, output_file, ifc_file.name)
            
            logger.info("\n=== NEXT STEPS FOR ALIGNMENT ===")
            logger.info("1. Use this point cloud as reference for alignment")
            logger.info("2. Load drone/geotiff point cloud as target")
            logger.info("3. Apply ICP or feature-based registration")
            logger.info("4. Focus on structural elements for key features")
            
        else:
            logger.error("Data-driven conversion failed or produced no points")
            logger.info("Check the strategy and element availability")
else:
    logger.error("No strategy available - run the metadata analysis first")

# Import visualization libraries
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
    logger.info("Matplotlib available for 2D visualization")
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    logger.warning("Matplotlib not available")

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
    logger.info("Plotly available for interactive 3D visualization")
except ImportError:
    PLOTLY_AVAILABLE = False
    logger.warning("Plotly not available")

try:
    import open3d as o3d
    O3D_AVAILABLE = True
    logger.info("Open3D available for 3D visualization")
except ImportError:
    O3D_AVAILABLE = False
    logger.warning("Open3D not available")

def visualize_element_analysis(element_analysis):
    """
    Visualize the element type analysis from metadata
    """
    if not MATPLOTLIB_AVAILABLE or 'counts' not in element_analysis:
        logger.warning("Cannot create element analysis visualization")
        return
    
    df_counts = element_analysis['counts']
    
    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # Bar chart of element counts
    top_elements = df_counts.head(10)  # Show top 10
    bars = ax1.bar(range(len(top_elements)), top_elements['Count'])
    ax1.set_xlabel('Element Type')
    ax1.set_ylabel('Count')
    ax1.set_title('Top 10 Element Types by Count')
    ax1.set_xticks(range(len(top_elements)))
    ax1.set_xticklabels([et.replace('Ifc', '') for et in top_elements['ElementType']], rotation=45, ha='right')
    
    # Add count labels on bars
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{int(height)}', ha='center', va='bottom', fontsize=9)
    
    # Pie chart of element distribution
    top_5 = df_counts.head(5)
    other_count = df_counts.iloc[5:]['Count'].sum() if len(df_counts) > 5 else 0
    
    pie_data = top_5['Count'].tolist()
    pie_labels = [et.replace('Ifc', '') for et in top_5['ElementType']]
    
    if other_count > 0:
        pie_data.append(other_count)
        pie_labels.append(f'Others ({len(df_counts)-5})')
    
    ax2.pie(pie_data, labels=pie_labels, autopct='%1.1f%%', startangle=90)
    ax2.set_title('Element Type Distribution')
    
    plt.tight_layout()
    plt.show()
    
    logger.info("Element analysis visualization complete")

def visualize_conversion_strategy(strategy):
    """
    Visualize the conversion strategy
    """
    if not MATPLOTLIB_AVAILABLE:
        logger.warning("Cannot create strategy visualization")
        return
    
    # Create strategy overview
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # Point allocation by element type
    if strategy['point_allocation']:
        elem_types = list(strategy['point_allocation'].keys())
        total_points = [strategy['point_allocation'][et]['total_points'] for et in elem_types]
        
        bars = ax1.bar(range(len(elem_types)), total_points)
        ax1.set_xlabel('Element Type')
        ax1.set_ylabel('Total Points Allocated')
        ax1.set_title('Point Allocation Strategy')
        ax1.set_xticks(range(len(elem_types)))
        ax1.set_xticklabels([et.replace('Ifc', '') for et in elem_types], rotation=45, ha='right')
        
        # Add point labels
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{int(height):,}', ha='center', va='bottom', fontsize=9)
    
    # Priority distribution
    priority_counts = {
        'Critical': len(strategy['available_critical']),
        'High': len(strategy['available_high']),
        'Medium': len(strategy['available_medium']),
        'Low': len(strategy['available_low']),
        'Other': len(strategy['other_elements'])
    }
    
    # Filter out zero counts
    priority_counts = {k: v for k, v in priority_counts.items() if v > 0}
    
    if priority_counts:
        colors = ['red', 'orange', 'yellow', 'lightblue', 'gray']
        color_map = dict(zip(priority_counts.keys(), colors[:len(priority_counts)]))
        
        wedges, texts, autotexts = ax2.pie(priority_counts.values(), 
                                          labels=priority_counts.keys(),
                                          autopct='%1.0f',
                                          colors=[color_map[k] for k in priority_counts.keys()],
                                          startangle=90)
        ax2.set_title('Available Elements by Priority')
    
    plt.tight_layout()
    plt.show()
    
    logger.info("Strategy visualization complete")

def visualize_point_cloud_2d(result, sample_size=10000):
    """
    Create 2D visualizations of the point cloud
    """
    if not MATPLOTLIB_AVAILABLE:
        logger.warning("Cannot create 2D point cloud visualization")
        return
    
    points = result['points']
    labels = result['labels']
    
    if len(points) == 0:
        logger.warning("No points to visualize")
        return
    
    # Sample points if too many
    if len(points) > sample_size:
        indices = np.random.choice(len(points), sample_size, replace=False)
        sampled_points = points[indices]
        sampled_labels = labels[indices]
        logger.info(f"Sampling {sample_size} points from {len(points)} total")
    else:
        sampled_points = points
        sampled_labels = labels
    
    # Create figure with subplots
    fig = plt.figure(figsize=(18, 12))
    
    # XY projection (top view)
    ax1 = plt.subplot(2, 3, 1)
    scatter = ax1.scatter(sampled_points[:, 0], sampled_points[:, 1], 
                         c=sampled_labels, s=1, alpha=0.6, cmap='tab10')
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_title('Top View (XY Projection)')
    ax1.grid(True, alpha=0.3)
    ax1.set_aspect('equal')
    
    # XZ projection (front view)
    ax2 = plt.subplot(2, 3, 2)
    ax2.scatter(sampled_points[:, 0], sampled_points[:, 2], 
               c=sampled_labels, s=1, alpha=0.6, cmap='tab10')
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Z (m)')
    ax2.set_title('Front View (XZ Projection)')
    ax2.grid(True, alpha=0.3)
    
    # YZ projection (side view)
    ax3 = plt.subplot(2, 3, 3)
    ax3.scatter(sampled_points[:, 1], sampled_points[:, 2], 
               c=sampled_labels, s=1, alpha=0.6, cmap='tab10')
    ax3.set_xlabel('Y (m)')
    ax3.set_ylabel('Z (m)')
    ax3.set_title('Side View (YZ Projection)')
    ax3.grid(True, alpha=0.3)
    
    # Point density heatmap (XY)
    ax4 = plt.subplot(2, 3, 4)
    hist, xedges, yedges = np.histogram2d(sampled_points[:, 0], sampled_points[:, 1], bins=50)
    extent = [xedges[0], xedges[-1], yedges[0], yedges[-1]]
    im = ax4.imshow(hist.T, extent=extent, origin='lower', cmap='hot', aspect='auto')
    ax4.set_xlabel('X (m)')
    ax4.set_ylabel('Y (m)')
    ax4.set_title('Point Density Heatmap')
    plt.colorbar(im, ax=ax4, label='Point Count')
    
    # Height distribution
    ax5 = plt.subplot(2, 3, 5)
    ax5.hist(sampled_points[:, 2], bins=50, alpha=0.7, edgecolor='black')
    ax5.set_xlabel('Z (m)')
    ax5.set_ylabel('Point Count')
    ax5.set_title('Height Distribution')
    ax5.grid(True, alpha=0.3)
    
    # Element type legend
    ax6 = plt.subplot(2, 3, 6)
    ax6.axis('off')
    
    # Create legend
    legend_text = "Element Types:\n\n"
    for elem_type, label in result['element_type_map'].items():
        if elem_type in result['processing_stats']:
            point_count = result['processing_stats'][elem_type]['points_generated']
            legend_text += f"{label}: {elem_type.replace('Ifc', '')} ({point_count:,} pts)\n"
    
    ax6.text(0.1, 0.9, legend_text, transform=ax6.transAxes, 
            fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.suptitle(f'Point Cloud Analysis - {len(points):,} Total Points', fontsize=16)
    plt.tight_layout()
    plt.show()
    
    logger.info("2D point cloud visualization complete")

def visualize_point_cloud_3d_interactive(result, sample_size=15000):
    """
    Create interactive 3D visualization using Plotly
    """
    if not PLOTLY_AVAILABLE:
        logger.warning("Plotly not available for interactive 3D visualization")
        return
    
    points = result['points']
    labels = result['labels']
    
    if len(points) == 0:
        logger.warning("No points to visualize")
        return
    
    # Sample points if too many
    if len(points) > sample_size:
        indices = np.random.choice(len(points), sample_size, replace=False)
        sampled_points = points[indices]
        sampled_labels = labels[indices]
        logger.info(f"Sampling {sample_size} points for 3D visualization")
    else:
        sampled_points = points
        sampled_labels = labels
    
    # Create element type names for hover
    element_names = []
    label_to_name = {v: k.replace('Ifc', '') for k, v in result['element_type_map'].items()}
    
    for label in sampled_labels:
        element_names.append(label_to_name.get(label, f'Type_{label}'))
    
    # Create 3D scatter plot
    fig = go.Figure(data=go.Scatter3d(
        x=sampled_points[:, 0],
        y=sampled_points[:, 1],
        z=sampled_points[:, 2],
        mode='markers',
        marker=dict(
            size=2,
            color=sampled_labels,
            colorscale='tab10',
            opacity=0.8,
            colorbar=dict(title="Element Type")
        ),
        text=element_names,
        hovertemplate='<b>%{text}</b><br>' +
                     'X: %{x:.2f}m<br>' +
                     'Y: %{y:.2f}m<br>' +
                     'Z: %{z:.2f}m<extra></extra>'
    ))
    
    fig.update_layout(
        title=f'Interactive 3D Point Cloud - {len(points):,} Points',
        scene=dict(
            xaxis_title='X (m)',
            yaxis_title='Y (m)',
            zaxis_title='Z (m)',
            aspectmode='data'
        ),
        width=900,
        height=700
    )
    
    fig.show()
    logger.info("Interactive 3D visualization complete")

def visualize_with_open3d(result, sample_size=20000):
    """
    Visualize point cloud with Open3D
    """
    if not O3D_AVAILABLE:
        logger.warning("Open3D not available for 3D visualization")
        return
    
    points = result['points']
    labels = result['labels']
    
    if len(points) == 0:
        logger.warning("No points to visualize")
        return
    
    # Sample points if too many
    if len(points) > sample_size:
        indices = np.random.choice(len(points), sample_size, replace=False)
        sampled_points = points[indices]
        sampled_labels = labels[indices]
        logger.info(f"Sampling {sample_size} points for Open3D visualization")
    else:
        sampled_points = points
        sampled_labels = labels
    
    # Create point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(sampled_points)
    
    # Generate colors based on labels
    unique_labels = np.unique(sampled_labels)
    colors = np.zeros((len(sampled_points), 3))
    
    # Color palette
    color_palette = [
        [1.0, 0.0, 0.0],  # Red
        [0.0, 1.0, 0.0],  # Green
        [0.0, 0.0, 1.0],  # Blue
        [1.0, 1.0, 0.0],  # Yellow
        [1.0, 0.0, 1.0],  # Magenta
        [0.0, 1.0, 1.0],  # Cyan
        [0.5, 0.5, 0.5],  # Gray
        [1.0, 0.5, 0.0],  # Orange
        [0.5, 0.0, 1.0],  # Purple
        [0.0, 0.5, 0.5],  # Teal
    ]
    
    for i, label in enumerate(unique_labels):
        color_idx = i % len(color_palette)
        mask = sampled_labels == label
        colors[mask] = color_palette[color_idx]
    
    pcd.colors = o3d.utility.Vector3dVector(colors)
    
    # Visualize
    logger.info("Opening Open3D visualization window...")
    o3d.visualization.draw_geometries([pcd], 
                                     window_name=f"Data-Driven Point Cloud - {len(points):,} Points",
                                     width=1200, height=800)
    
    logger.info("Open3D visualization complete")

# Visualize element analysis (if metadata was loaded)
if 'element_analysis' in locals() and element_analysis:
    logger.info("\n=== VISUALIZING ELEMENT ANALYSIS ===")
    visualize_element_analysis(element_analysis)
else:
    logger.info("No element analysis data available for visualization")

# Visualize conversion strategy (if strategy was created)
if 'strategy' in locals() and strategy:
    logger.info("\n=== VISUALIZING CONVERSION STRATEGY ===")
    visualize_conversion_strategy(strategy)
else:
    logger.info("No conversion strategy available for visualization")

# Visualize point cloud results (if conversion was successful)
if 'result' in locals() and result and len(result['points']) > 0:
    logger.info("\n=== VISUALIZING POINT CLOUD RESULTS ===")
    
    # 2D visualizations
    logger.info("Creating 2D visualizations...")
    visualize_point_cloud_2d(result, sample_size=10000)
    
    # Interactive 3D visualization
    if PLOTLY_AVAILABLE:
        logger.info("Creating interactive 3D visualization...")
        visualize_point_cloud_3d_interactive(result, sample_size=15000)
    
    # Open3D visualization
    if O3D_AVAILABLE:
        logger.info("Opening Open3D 3D visualization...")
        visualize_with_open3d(result, sample_size=20000)
    
else:
    logger.info("No point cloud results available for visualization")

def assess_point_cloud_quality(result):
    """
    Comprehensive quality assessment for alignment readiness
    """
    if not result or len(result['points']) == 0:
        logger.error("No point cloud data to assess")
        return
    
    points = result['points']
    
    logger.info("\n=== COMPREHENSIVE QUALITY ASSESSMENT ===")
    
    # Basic statistics
    logger.info(f"Total Points: {len(points):,}")
    logger.info(f"Element Types: {len(result['element_type_map'])}")
    
    # Spatial coverage
    x_span = points[:, 0].max() - points[:, 0].min()
    y_span = points[:, 1].max() - points[:, 1].min()
    z_span = points[:, 2].max() - points[:, 2].min()
    area = x_span * y_span
    
    logger.info(f"\nSpatial Coverage:")
    logger.info(f"  X span: {x_span:.1f}m")
    logger.info(f"  Y span: {y_span:.1f}m")
    logger.info(f"  Z span: {z_span:.1f}m")
    logger.info(f"  Coverage area: {area:.1f}m²")
    
    # Point density
    point_density = len(points) / area if area > 0 else 0
    logger.info(f"  Point density: {point_density:.2f} points/m²")
    
    # Element type analysis
    logger.info(f"\nElement Type Analysis:")
    critical_elements = ['IfcWall', 'IfcRoof', 'IfcCurtainWall']
    high_priority_elements = ['IfcSlab', 'IfcBuildingElementProxy']
    
    critical_found = [elem for elem in critical_elements if elem in result['element_type_map']]
    high_priority_found = [elem for elem in high_priority_elements if elem in result['element_type_map']]
    
    logger.info(f"  Critical elements found: {critical_found}")
    logger.info(f"  High priority elements found: {high_priority_found}")
    
    # Alignment readiness score
    score = 0
    max_score = 100
    
    # Point count (30 points max)
    if len(points) >= 10000:
        score += 30
    elif len(points) >= 5000:
        score += 20
    elif len(points) >= 3000:
        score += 15
    else:
        score += 5
    
    # Critical elements (25 points max)
    score += len(critical_found) * 8
    
    # High priority elements (20 points max)
    score += len(high_priority_found) * 10
    
    # Coverage area (15 points max)
    if area >= 1000:
        score += 15
    elif area >= 500:
        score += 10
    elif area >= 100:
        score += 5
    
    # Point density (10 points max)
    if point_density >= 10:
        score += 10
    elif point_density >= 5:
        score += 7
    elif point_density >= 1:
        score += 5
    
    score = min(score, max_score)
    
    logger.info(f"\n=== ALIGNMENT READINESS SCORE: {score}/100 ===")
    
    if score >= 80:
        logger.info("🟢 EXCELLENT - Highly suitable for alignment")
    elif score >= 60:
        logger.info("🟡 GOOD - Suitable for alignment with minor limitations")
    elif score >= 40:
        logger.info("🟠 FAIR - May work for alignment but consider improvements")
    else:
        logger.info("🔴 POOR - Significant improvements needed for reliable alignment")
    
    # Recommendations
    logger.info("\nRecommendations:")
    if len(points) < 5000:
        logger.info("  - Increase point sampling or include more element types")
    if not critical_found:
        logger.info("  - Try to include walls or roofs for better alignment features")
    if point_density < 5:
        logger.info("  - Increase points per element for better coverage")
    if area < 500:
        logger.info("  - Include more building elements for larger coverage area")
    
    return score

# Run quality assessment
if 'result' in locals() and result:
    quality_score = assess_point_cloud_quality(result)
    
    logger.info("\n=== FINAL RECOMMENDATIONS FOR ALIGNMENT ===")
    logger.info("1. Use this point cloud as reference geometry")
    logger.info("2. Load drone/geotiff point cloud as target")
    logger.info("3. Apply preprocessing (downsampling, filtering) if needed")
    logger.info("4. Use ICP or feature-based registration algorithms")
    logger.info("5. Focus on structural elements for robust alignment")
    
    if quality_score >= 60:
        logger.info("\n✅ Point cloud is ready for alignment processing!")
    else:
        logger.info("\n⚠️  Consider running metadata extraction again or adjusting strategy")
else:
    logger.info("No results available for quality assessment")

import bpy
import numpy as np
import os
import logging
from pathlib import Path
import open3d as o3d

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
ifc_file = "/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc"
output_dir = "../../data/output_runs/data/ifc_pointclouds"
output_ply = "ifc_pointcloud.ply"
output_xyz = "ifc_pointcloud.xyz"
element_limit = 5000  # Start with 5,000 elements to manage large model
sample_density = 0.01  # Sampling distance in meters (adjust for point density)

# Setup paths
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)
full_ply_path = output_path / output_ply
full_xyz_path = output_path / output_xyz

print(f"Processing IFC file: {ifc_file}")
print(f"Output directory: {output_path}")
print(f"Output PLY: {full_ply_path}")
print(f"Output XYZ: {full_xyz_path}")
print(f"Element limit: {element_limit}")
print(f"Sample density: {sample_density}m")
print(f"Date and Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} IST")

# Clear existing data
bpy.ops.wm.read_factory_settings(use_empty=True)
bpy.ops.bim.load_project(filepath=str(ifc_file), 
                        should_start_fresh_session=True, 
                        use_detailed_tooltip=False, 
                        element_limit=element_limit)

# Filter and process relevant elements (e.g., IfcColumn for structural focus)
points = []
for obj in bpy.data.objects:
    if obj.type == 'MESH':
        # Check IFC type (approximate based on BlenderBIM naming)
        if "IfcColumn" in obj.name or "IfcBuildingElementProxy" in obj.name:
            logger.info(f"Processing {obj.name}")
            bpy.context.view_layer.objects.active = obj
            bpy.ops.object.mode_set(mode='EDIT')
            bpy.ops.mesh.select_all(action='SELECT')
            bpy.ops.object.mode_set(mode='OBJECT')
            mesh = obj.data
            for v in mesh.vertices:
                points.append(v.co)

# Convert to numpy array
points = np.array(points)
logger.info(f"Extracted {points.shape[0]} points from {len(bpy.data.objects)} objects")

# Optional downsampling for higher density
if sample_density > 0:
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd = pcd.uniform_down_sample(sample_density)
    points = np.asarray(pcd.points)
    logger.info(f"Downsampled to {points.shape[0]} points with density {sample_density}m")

# Save as XYZ
np.savetxt(str(full_xyz_path), points, fmt="%f %f %f", delimiter=" ")
logger.info(f"Saved point cloud to {full_xyz_path}")

# Save as PLY
pcd.points = o3d.utility.Vector3dVector(points)
o3d.io.write_point_cloud(str(full_ply_path), pcd)
logger.info(f"Saved point cloud to {full_ply_path}")

print(f"Conversion completed. Total points: {points.shape[0]}")

# minimal_ifc_to_pointcloud_conversion.ipynb

import ifcopenshell
import ifcopenshell.geom
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.express as px
from pathlib import Path
import open3d as o3d

# 1. Load IFC and count elements
def load_ifc_metadata(ifc_path):
    model = ifcopenshell.open(ifc_path)
    elements = model.by_type("IfcProduct")
    counts = {}
    for elem in elements:
        typename = elem.is_a()
        counts[typename] = counts.get(typename, 0) + 1
    return counts

# 2. Create conversion strategy (simple sampling rule)
def create_strategy(counts, sample_per_elem=20):
    return {k: min(v, 10000) * sample_per_elem for k, v in counts.items() if "Ifc" in k}

# 3. Convert to XYZ points using geometry engine
def convert_to_point_cloud(ifc_path, strategy):
    model = ifcopenshell.open(ifc_path)
    settings = ifcopenshell.geom.settings()
    settings.set(settings.USE_WORLD_COORDS, True)

    points = []
    for ifc_type, est_points in strategy.items():
        elems = model.by_type(ifc_type)
        for elem in elems:
            try:
                shape = ifcopenshell.geom.create_shape(settings, elem)
                verts = np.array(shape.geometry.verts).reshape(-1, 3)
                points.append(verts)
            except:
                continue
    return np.vstack(points) if points else np.array([])

# 4. Visualizations
def visualize_2d(points):
    if points.shape[0] == 0:
        print("No points to visualize.")
        return
    plt.figure(figsize=(8, 6))
    plt.scatter(points[:, 0], points[:, 1], s=0.5, alpha=0.6)
    plt.title("2D View (X-Y Plane)")
    plt.xlabel("X")
    plt.ylabel("Y")
    plt.axis("equal")
    plt.grid(True)
    plt.show()

def visualize_3d(points):
    if points.shape[0] == 0:
        print("No points to visualize.")
        return
    df = pd.DataFrame(points, columns=["x", "y", "z"])
    fig = px.scatter_3d(df.sample(min(len(df), 15000)), x='x', y='y', z='z', opacity=0.6)
    fig.update_layout(title="3D Point Cloud", height=700)
    fig.show()

# 5. Run full flow
if __name__ == "__main__":
    ifc_file = "GRE.EEC.S.00.IT.P.14353.00.265.ifc"  # Change to your IFC file
    ifc_path = Path("../../../data/raw/trino_enel/ifc") / ifc_file

    counts = load_ifc_metadata(str(ifc_path))
    strategy = create_strategy(counts)
    points = convert_to_point_cloud(str(ifc_path), strategy)

    print(f"✅ Total points: {points.shape[0]}")
    visualize_2d(points)
    visualize_3d(points)
