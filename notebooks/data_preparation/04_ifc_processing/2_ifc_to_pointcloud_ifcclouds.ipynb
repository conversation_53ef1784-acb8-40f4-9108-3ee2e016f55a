import os
import sys
import logging
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import ifcopenshell
import open3d as o3d


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from ifcclouds.convert import process_ifc
except ImportError:
    import os
    print("Installing ifcclouds...")
    os.system("pip install python-dotenv tqdm numpy open3d plyfile")
    os.system("pip install git+https://github.com/D4ve-R/ifcclouds.git")
    from ifcclouds.convert import process_ifc

import IPython
IPython.Application.instance().kernel.do_shutdown(True)



# Set up paths
base_path = Path("../../../data/raw/trino_enel/ifc")
output_path = Path("../../../data/processed/trino_enel/ifc_pointclouds")
output_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Base path: {base_path}")
logger.info(f"Output path: {output_path}")

# Find IFC files
ifc_files = list(base_path.glob("*.ifc"))
logger.info(f"Found {len(ifc_files)} IFC files")
for ifc_file in ifc_files:
    logger.info(f"  - {ifc_file.name}")

def convert_ifc_with_ifcclouds(ifc_file_path, output_dir):
    """
    Convert IFC file to point cloud using ifcclouds library.
    output_dir should be a directory (not a .ply file).
    """
    try:
        logger.info(f"Converting {ifc_file_path.name} to point cloud")
        output_dir.mkdir(parents=True, exist_ok=True)

        # Convert IFC to point cloud using ifcclouds
        process_ifc(str(ifc_file_path), str(output_dir))

        logger.info(f"Successfully converted {ifc_file_path.name} to directory {output_dir}")
        return True

    except Exception as e:
        logger.error(f"Error converting IFC file: {e}")
        return False

if ifc_files:
    ifc_file = ifc_files[0]

    # Use a subdirectory named after the IFC file (without extension)
    output_dir = output_path / ifc_file.stem
    output_dir.mkdir(parents=True, exist_ok=True)

    print("Output directory:", output_dir)

    success = convert_ifc_with_ifcclouds(ifc_file, output_dir)

    if success:
        logger.info(f"Point cloud saved in directory: {output_dir}")
    else:
        logger.error("Failed to convert IFC file")
else:
    logger.warning("No IFC files found.")


def load_ply_pointcloud(ply_file_path):
    """
    Load point cloud from PLY file
    """
    try:
        pcd = o3d.io.read_point_cloud(str(ply_file_path))
        points = np.asarray(pcd.points)

        # Try to read labels if available
        labels = None
        if hasattr(pcd, 'colors') and len(pcd.colors) > 0:
            # ifcclouds saves class labels as the 4th column
            # You would need to read them using PlyData if needed
            pass

        logger.info(f"Loaded point cloud with {len(points)} points")
        return points, labels

    except Exception as e:
        logger.error(f"Error loading point cloud: {e}")
        return None, None

# Load the converted point cloud
if 'output_file' in locals() and output_file.exists():
    points, labels = load_ply_pointcloud(output_file)
    
    if points is not None:
        logger.info(f"Point cloud statistics:")
        logger.info(f"  Number of points: {len(points)}")
        logger.info(f"  X range: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
        logger.info(f"  Y range: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
        logger.info(f"  Z range: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
else:
    logger.warning("No point cloud file available to load")

def visualize_pointcloud_2d(points, title="Point Cloud", sample_size=10000):
    """
    Visualize point cloud using matplotlib 2D projections
    """
    if points is None or len(points) == 0:
        logger.warning("No points to visualize")
        return
    
    # Sample points if too many
    if len(points) > sample_size:
        indices = np.random.choice(len(points), sample_size, replace=False)
        sampled_points = points[indices]
        logger.info(f"Sampling {sample_size} points from {len(points)} total points")
    else:
        sampled_points = points
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f"{title} - {len(points)} points", fontsize=16)
    
    # XY projection
    axes[0, 0].scatter(sampled_points[:, 0], sampled_points[:, 1], s=0.5, alpha=0.6)
    axes[0, 0].set_xlabel('X')
    axes[0, 0].set_ylabel('Y')
    axes[0, 0].set_title('XY Projection (Top View)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # XZ projection
    axes[0, 1].scatter(sampled_points[:, 0], sampled_points[:, 2], s=0.5, alpha=0.6)
    axes[0, 1].set_xlabel('X')
    axes[0, 1].set_ylabel('Z')
    axes[0, 1].set_title('XZ Projection (Front View)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # YZ projection
    axes[1, 0].scatter(sampled_points[:, 1], sampled_points[:, 2], s=0.5, alpha=0.6)
    axes[1, 0].set_xlabel('Y')
    axes[1, 0].set_ylabel('Z')
    axes[1, 0].set_title('YZ Projection (Side View)')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 3D scatter (pseudo-3D)
    ax_3d = fig.add_subplot(2, 2, 4, projection='3d')
    ax_3d.scatter(sampled_points[:, 0], sampled_points[:, 1], sampled_points[:, 2], s=0.5, alpha=0.6)
    ax_3d.set_xlabel('X')
    ax_3d.set_ylabel('Y')
    ax_3d.set_zlabel('Z')
    ax_3d.set_title('3D View')
    
    plt.tight_layout()
    plt.show()

# Visualize the point cloud
if 'points' in locals() and points is not None:
    visualize_pointcloud_2d(points, "IFC Point Cloud", sample_size=20000)
else:
    logger.warning("No point cloud data available for visualization")

# Process all IFC files
if ifc_files:
    logger.info(f"Processing {len(ifc_files)} IFC files")

    successful_conversions = 0
    failed_conversions = 0

    for ifc_file in ifc_files:
        # Create a dedicated output directory for each IFC file
        output_dir = output_path / ifc_file.stem
        output_dir.mkdir(parents=True, exist_ok=True)

        # Construct expected PLY file path
        ply_file = output_dir / f"{ifc_file.stem}.ply"

        if ply_file.exists():
            logger.info(f"Skipping {ifc_file.name} - output already exists at {ply_file}")
            successful_conversions += 1
            continue

        success = convert_ifc_with_ifcclouds(ifc_file, output_dir)

        if success:
            successful_conversions += 1
        else:
            failed_conversions += 1

    logger.info("Batch processing complete:")
    logger.info(f"  Successful conversions: {successful_conversions}")
    logger.info(f"  Failed conversions: {failed_conversions}")
else:
    logger.warning("Cannot perform batch processing - no IFC files found")


# List generated point cloud files
ply_files = list(output_path.glob("*.ply"))
logger.info(f"Generated point cloud files ({len(ply_files)}):")

for ply_file in ply_files:
    file_size_mb = ply_file.stat().st_size / (1024 * 1024)
    logger.info(f"  - {ply_file.name} ({file_size_mb:.2f} MB)")