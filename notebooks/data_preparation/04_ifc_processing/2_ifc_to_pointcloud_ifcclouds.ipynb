{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC to Point Cloud Converter using ifcclouds\n", "\n", "This notebook converts IFC (Industry Foundation Classes) files to point clouds using the D4ve-R/ifcclouds library.\n", "\n", "Features:\n", "1. Load IFC files and extract geometry using ifcclouds\n", "2. Convert IFC elements to point clouds with class labels\n", "3. Visualize the resulting point clouds\n", "4. Save point clouds to PLY format\n", "5. Filter IFC elements by type\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** July 2025"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Dependencies"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import logging\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import ifcopenshell\n", "import open3d as o3d\n", "\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'status': 'ok', 'restart': True}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["try:\n", "    from ifcclouds.convert import process_ifc\n", "except ImportError:\n", "    import os\n", "    print(\"Installing ifcclouds...\")\n", "    os.system(\"pip install python-dotenv tqdm numpy open3d plyfile\")\n", "    os.system(\"pip install git+https://github.com/D4ve-R/ifcclouds.git\")\n", "    from ifcclouds.convert import process_ifc\n", "\n", "import IPython\n", "IPython.Application.instance().kernel.do_shutdown(True)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration and File Paths"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:21:24,666 - INFO - Base path: ../../../data/raw/trino_enel/ifc\n", "2025-07-02 13:21:24,666 - INFO - Output path: ../../../data/processed/trino_enel/ifc_pointclouds\n"]}], "source": ["# Set up paths\n", "base_path = Path(\"../../../data/raw/trino_enel/ifc\")\n", "output_path = Path(\"../../../data/processed/trino_enel/ifc_pointclouds\")\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Base path: {base_path}\")\n", "logger.info(f\"Output path: {output_path}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:21:24,671 - INFO - Found 1 IFC files\n", "2025-07-02 13:21:24,672 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265.ifc\n"]}], "source": ["# Find IFC files\n", "ifc_files = list(base_path.glob(\"*.ifc\"))\n", "logger.info(f\"Found {len(ifc_files)} IFC files\")\n", "for ifc_file in ifc_files:\n", "    logger.info(f\"  - {ifc_file.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. IFC to Point Cloud Conversion using ifcclouds"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def convert_ifc_with_ifcclouds(ifc_file_path, output_dir):\n", "    \"\"\"\n", "    Convert IFC file to point cloud using ifcclouds library.\n", "    output_dir should be a directory (not a .ply file).\n", "    \"\"\"\n", "    try:\n", "        logger.info(f\"Converting {ifc_file_path.name} to point cloud\")\n", "        output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "        # Convert IFC to point cloud using ifcclouds\n", "        process_ifc(str(ifc_file_path), str(output_dir))\n", "\n", "        logger.info(f\"Successfully converted {ifc_file_path.name} to directory {output_dir}\")\n", "        return True\n", "\n", "    except Exception as e:\n", "        logger.error(f\"Error converting IFC file: {e}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:21:24,680 - INFO - Converting GRE.EEC.S.00.IT.P.14353.00.265.ifc to point cloud\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Output directory: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:21:32,378 - ERROR - Error converting IFC file: need at least one array to concatenate\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Error loading classes from /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ifcclouds/data/classes.json, return default\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:21:34,236 - ERROR - Failed to convert IFC file\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["if ifc_files:\n", "    ifc_file = ifc_files[0]\n", "\n", "    # Use a subdirectory named after the IFC file (without extension)\n", "    output_dir = output_path / ifc_file.stem\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    print(\"Output directory:\", output_dir)\n", "\n", "    success = convert_ifc_with_ifcclouds(ifc_file, output_dir)\n", "\n", "    if success:\n", "        logger.info(f\"Point cloud saved in directory: {output_dir}\")\n", "    else:\n", "        logger.error(\"Failed to convert IFC file\")\n", "else:\n", "    logger.warning(\"No IFC files found.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON> and <PERSON><PERSON>ze Point Cloud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_ply_pointcloud(ply_file_path):\n", "    \"\"\"\n", "    Load point cloud from PLY file\n", "    \"\"\"\n", "    try:\n", "        pcd = o3d.io.read_point_cloud(str(ply_file_path))\n", "        points = np.asarray(pcd.points)\n", "\n", "        # Try to read labels if available\n", "        labels = None\n", "        if hasattr(pcd, 'colors') and len(pcd.colors) > 0:\n", "            # ifcclouds saves class labels as the 4th column\n", "            # You would need to read them using PlyData if needed\n", "            pass\n", "\n", "        logger.info(f\"Loaded point cloud with {len(points)} points\")\n", "        return points, labels\n", "\n", "    except Exception as e:\n", "        logger.error(f\"Error loading point cloud: {e}\")\n", "        return None, None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:21:01,009 - WARNING - No point cloud file available to load\n"]}], "source": ["# Load the converted point cloud\n", "if 'output_file' in locals() and output_file.exists():\n", "    points, labels = load_ply_pointcloud(output_file)\n", "    \n", "    if points is not None:\n", "        logger.info(f\"Point cloud statistics:\")\n", "        logger.info(f\"  Number of points: {len(points)}\")\n", "        logger.info(f\"  X range: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]\")\n", "        logger.info(f\"  Y range: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]\")\n", "        logger.info(f\"  Z range: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]\")\n", "else:\n", "    logger.warning(\"No point cloud file available to load\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_pointcloud_2d(points, title=\"Point Cloud\", sample_size=10000):\n", "    \"\"\"\n", "    Visualize point cloud using matplotlib 2D projections\n", "    \"\"\"\n", "    if points is None or len(points) == 0:\n", "        logger.warning(\"No points to visualize\")\n", "        return\n", "    \n", "    # Sample points if too many\n", "    if len(points) > sample_size:\n", "        indices = np.random.choice(len(points), sample_size, replace=False)\n", "        sampled_points = points[indices]\n", "        logger.info(f\"Sampling {sample_size} points from {len(points)} total points\")\n", "    else:\n", "        sampled_points = points\n", "    \n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle(f\"{title} - {len(points)} points\", fontsize=16)\n", "    \n", "    # XY projection\n", "    axes[0, 0].scatter(sampled_points[:, 0], sampled_points[:, 1], s=0.5, alpha=0.6)\n", "    axes[0, 0].set_xlabel('X')\n", "    axes[0, 0].set_ylabel('Y')\n", "    axes[0, 0].set_title('XY Projection (Top View)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # XZ projection\n", "    axes[0, 1].scatter(sampled_points[:, 0], sampled_points[:, 2], s=0.5, alpha=0.6)\n", "    axes[0, 1].set_xlabel('X')\n", "    axes[0, 1].set_ylabel('Z')\n", "    axes[0, 1].set_title('XZ Projection (Front View)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # YZ projection\n", "    axes[1, 0].scatter(sampled_points[:, 1], sampled_points[:, 2], s=0.5, alpha=0.6)\n", "    axes[1, 0].set_xlabel('Y')\n", "    axes[1, 0].set_ylabel('Z')\n", "    axes[1, 0].set_title('YZ Projection (Side View)')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 3D scatter (pseudo-3D)\n", "    ax_3d = fig.add_subplot(2, 2, 4, projection='3d')\n", "    ax_3d.scatter(sampled_points[:, 0], sampled_points[:, 1], sampled_points[:, 2], s=0.5, alpha=0.6)\n", "    ax_3d.set_xlabel('X')\n", "    ax_3d.set_ylabel('Y')\n", "    ax_3d.set_zlabel('Z')\n", "    ax_3d.set_title('3D View')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:21:01,019 - WARNING - No point cloud data available for visualization\n"]}], "source": ["# Visualize the point cloud\n", "if 'points' in locals() and points is not None:\n", "    visualize_pointcloud_2d(points, \"IFC Point Cloud\", sample_size=20000)\n", "else:\n", "    logger.warning(\"No point cloud data available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON> Processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:21:01,023 - INFO - Processing 1 IFC files\n", "2025-07-02 13:21:01,024 - INFO - Converting GRE.EEC.S.00.IT.P.14353.00.265.ifc to point cloud\n", "2025-07-02 13:21:01,024 - ERROR - Error converting IFC file: process_ifc() got an unexpected keyword argument 'ifc_path'\n", "2025-07-02 13:21:01,024 - INFO - Batch processing complete:\n", "2025-07-02 13:21:01,024 - INFO -   Successful conversions: 0\n", "2025-07-02 13:21:01,025 - INFO -   Failed conversions: 1\n"]}], "source": ["# Process all IFC files\n", "if ifc_files:\n", "    logger.info(f\"Processing {len(ifc_files)} IFC files\")\n", "\n", "    successful_conversions = 0\n", "    failed_conversions = 0\n", "\n", "    for ifc_file in ifc_files:\n", "        # Create a dedicated output directory for each IFC file\n", "        output_dir = output_path / ifc_file.stem\n", "        output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "        # Construct expected PLY file path\n", "        ply_file = output_dir / f\"{ifc_file.stem}.ply\"\n", "\n", "        if ply_file.exists():\n", "            logger.info(f\"Skipping {ifc_file.name} - output already exists at {ply_file}\")\n", "            successful_conversions += 1\n", "            continue\n", "\n", "        success = convert_ifc_with_ifcclouds(ifc_file, output_dir)\n", "\n", "        if success:\n", "            successful_conversions += 1\n", "        else:\n", "            failed_conversions += 1\n", "\n", "    logger.info(\"Batch processing complete:\")\n", "    logger.info(f\"  Successful conversions: {successful_conversions}\")\n", "    logger.info(f\"  Failed conversions: {failed_conversions}\")\n", "else:\n", "    logger.warning(\"Cannot perform batch processing - no IFC files found\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Summary and Output Files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-02 13:21:01,028 - INFO - Generated point cloud files (1):\n", "2025-07-02 13:21:01,029 - INFO -   - GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply (0.00 MB)\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# List generated point cloud files\n", "ply_files = list(output_path.glob(\"*.ply\"))\n", "logger.info(f\"Generated point cloud files ({len(ply_files)}):\")\n", "\n", "for ply_file in ply_files:\n", "    file_size_mb = ply_file.stat().st_size / (1024 * 1024)\n", "    logger.info(f\"  - {ply_file.name} ({file_size_mb:.2f} MB)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}