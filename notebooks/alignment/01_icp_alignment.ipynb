{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - can be overridden during execution\n", "project_type = \"foundation_analysis\"  # Project type for data organization\n", "site_name = \"castro_area4\"            # Site identifier for processing\n", "source_file = \"source_pointcloud.las\"  # Source point cloud file\n", "target_file = \"target_pointcloud.las\"  # Target point cloud file\n", "max_iterations = 50\n", "tolerance = 1e-6\n", "voxel_size = 0.02\n", "distance_threshold = 0.1\n", "output_dir = \"../../data/output_runs\"\n", "mlflow_experiment_name = \"alignment_icp\"\n", "mlflow_run_name = f\"icp_{project_type}_{site_name}\"\n", "enable_visualization = True\n", "save_intermediate = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ICP-Based Point Cloud Alignment\n", "\n", "This notebook implements traditional Iterative Closest Point (ICP) alignment for point cloud registration. It provides a comprehensive implementation with modular execution cells for clarity and detailed analysis of ICP performance.\n", "\n", "**Stage**: Alignment  \n", "**Input Data**: Source and target point clouds  \n", "**Output**: Aligned point cloud with transformation matrix  \n", "**Method**: Traditional ICP with multiple variants  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Process Overview:\n", "1. **Environment Setup**: Import libraries and configure parameters\n", "2. **Data Loading**: Load source and target point clouds\n", "3. **Preprocessing**: Normalize and downsample point clouds\n", "4. **ICP Implementation**: Multiple ICP variants and optimizations\n", "5. **Evaluation**: Performance metrics and quality assessment\n", "6. **Visualization**: Comprehensive alignment results\n", "7. **Export**: Save aligned point clouds and metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "Configure the environment with required libraries and parameters for ICP alignment."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: open3d in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (0.19.0)\n", "Requirement already satisfied: matplotlib in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (3.10.3)\n", "Requirement already satisfied: laspy in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (2.5.4)\n", "Collecting transforms3d\n", "  Using cached transforms3d-0.4.2-py3-none-any.whl.metadata (2.8 kB)\n", "Requirement already satisfied: scipy in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (1.16.0)\n", "Requirement already satisfied: pandas in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (2.3.0)\n", "Requirement already satisfied: mlflow in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (3.1.1)\n", "Requirement already satisfied: numpy>=1.18.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (1.26.4)\n", "Requirement already satisfied: dash>=2.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.0.4)\n", "Requirement already satisfied: werkzeug>=3.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.0.6)\n", "Requirement already satisfied: flask>=3.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (3.0.3)\n", "Requirement already satisfied: nbformat>=5.7.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from open3d) (5.10.4)\n", "Requirement already satisfied: configargparse in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (1.7.1)\n", "Requirement already satisfied: addict in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (2.4.0)\n", "Requirement already satisfied: pillow>=9.3.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (11.2.1)\n", "Requirement already satisfied: pyyaml>=5.4.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (6.0.2)\n", "Requirement already satisfied: scikit-learn>=0.21 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (1.7.0)\n", "Requirement already satisfied: tqdm in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (4.67.1)\n", "Requirement already satisfied: pyquaternion in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from open3d) (0.9.9)\n", "Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib) (25.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: mlflow-skinny==3.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (3.1.1)\n", "Requirement already satisfied: alembic!=1.10.0,<2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (1.16.2)\n", "Requirement already satisfied: docker<8,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (7.1.0)\n", "Requirement already satisfied: graphene<4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (3.4.3)\n", "Requirement already satisfied: gunicorn<24 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (23.0.0)\n", "Requirement already satisfied: pyarrow<21,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (20.0.0)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow) (2.0.41)\n", "Requirement already satisfied: cachetools<7,>=5.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (5.5.2)\n", "Requirement already satisfied: click<9,>=7.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (8.2.1)\n", "Requirement already satisfied: cloudpickle<4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.1.1)\n", "Requirement already satisfied: databricks-sdk<1,>=0.20.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.57.0)\n", "Requirement already satisfied: fastapi<1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.115.14)\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.1.44)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (8.7.0)\n", "Requirement already satisfied: opentelemetry-api<3,>=1.9.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (1.34.1)\n", "Requirement already satisfied: opentelemetry-sdk<3,>=1.9.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (1.34.1)\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (3.20.3)\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (2.11.7)\n", "Requirement already satisfied: requests<3,>=2.17.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (2.32.4)\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.5.3)\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (4.14.0)\n", "Requirement already satisfied: uvicorn<1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from mlflow-skinny==3.1.1->mlflow) (0.34.3)\n", "Requirement already satisfied: <PERSON><PERSON> in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from alembic!=1.10.0,<2->mlflow) (1.3.10)\n", "Requirement already satisfied: google-auth~=2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (2.40.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\n", "Requirement already satisfied: starlette<0.47.0,>=0.40.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from fastapi<1->mlflow-skinny==3.1.1->mlflow) (0.46.2)\n", "Requirement already satisfied: Jinja2>=3.1.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (3.1.6)\n", "Requirement already satisfied: itsdangerous>=2.1.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (2.2.0)\n", "Requirement already satisfied: blinker>=1.6.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from flask>=3.0.0->open3d) (1.9.0)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow) (4.0.12)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.1.1->mlflow) (5.0.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (4.9.1)\n", "Requirement already satisfied: graphql-core<3.3,>=3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from graphene<4->mlflow) (3.2.6)\n", "Requirement already satisfied: graphql-relay<3.3,>=3.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from graphene<4->mlflow) (3.2.0)\n", "Requirement already satisfied: zipp>=3.20 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.1.1->mlflow) (3.23.0)\n", "Requirement already satisfied: opentelemetry-semantic-conventions==0.55b1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.1.1->mlflow) (0.55b1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.1.1->mlflow) (0.4.1)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (3.10)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from requests<3,>=2.17.3->mlflow-skinny==3.1.1->mlflow) (2025.6.15)\n", "Requirement already satisfied: pyasn1>=0.1.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rsa<5,>=3.1.4->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.1.1->mlflow) (0.6.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from scikit-learn>=0.21->open3d) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from scikit-learn>=0.21->open3d) (3.6.0)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow) (4.9.0)\n", "Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from anyio<5,>=3.6.2->starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==3.1.1->mlflow) (1.3.1)\n", "Requirement already satisfied: h11>=0.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from uvicorn<1->mlflow-skinny==3.1.1->mlflow) (0.16.0)\n", "Requirement already satisfied: plotly>=5.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (6.1.2)\n", "Requirement already satisfied: retrying in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (1.3.5)\n", "Requirement already satisfied: nest-asyncio in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (1.6.0)\n", "Requirement already satisfied: setuptools in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from dash>=2.6.0->open3d) (80.9.0)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from werkzeug>=3.0.0->open3d) (3.0.2)\n", "Requirement already satisfied: fastjsonschema>=2.15 in /Users/<USER>/.local/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (2.21.1)\n", "Requirement already satisfied: jsonschema>=2.6 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (4.24.0)\n", "Requirement already satisfied: jupyter-core!=5.0.*,>=4.12 in /Users/<USER>/.local/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (5.7.2)\n", "Requirement already satisfied: traitlets>=5.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from nbformat>=5.7.0->open3d) (5.14.3)\n", "Requirement already satisfied: attrs>=22.2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jsonschema>=2.6->nbformat>=5.7.0->open3d) (0.25.1)\n", "Requirement already satisfied: platformdirs>=2.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from jupyter-core!=5.0.*,>=4.12->nbformat>=5.7.0->open3d) (4.3.8)\n", "Requirement already satisfied: narwhals>=1.15.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from plotly>=5.0.0->dash>=2.6.0->open3d) (1.44.0)\n", "Using cached transforms3d-0.4.2-py3-none-any.whl (1.4 MB)\n", "Installing collected packages: transforms3d\n", "Successfully installed transforms3d-0.4.2\n"]}], "source": ["# Install required packages\n", "!pip install open3d matplotlib laspy transforms3d scipy pandas mlflow"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MLflow available for experiment tracking\n", "ICP Alignment Environment Initialized\n", "Open3D version: 0.19.0\n", "Analysis Date: 2025-07-02 15:43:53\n"]}], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import laspy\n", "import logging\n", "import time\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "import pandas as pd\n", "import json\n", "\n", "# MLflow tracking\n", "try:\n", "    import mlflow\n", "    import mlflow.sklearn\n", "    MLFLOW_AVAILABLE = True\n", "    print(\"MLflow available for experiment tracking\")\n", "except ImportError:\n", "    MLFLOW_AVAILABLE = False\n", "    print(\"MLflow not available - install with: pip install mlflow\")\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"ICP Alignment Environment Initialized\")\n", "print(f\"Open3D version: {o3d.__version__}\")\n", "print(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration Parameters\n", "\n", "Define ICP algorithm parameters and data paths."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'project_name' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 28\u001b[39m\n\u001b[32m     25\u001b[39m         \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mSource file: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m.SOURCE_FILE\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     26\u001b[39m         \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mTarget file: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m.TARGET_FILE\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m28\u001b[39m config = \u001b[43mICPConfig\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     30\u001b[39m \u001b[38;5;66;03m# Initialize MLflow if available\u001b[39;00m\n\u001b[32m     31\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m MLFLOW_AVAILABLE:\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 7\u001b[39m, in \u001b[36mICPConfig.__init__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m      5\u001b[39m     \u001b[38;5;66;03m# Use Papermill parameters\u001b[39;00m\n\u001b[32m      6\u001b[39m     \u001b[38;5;28mself\u001b[39m.PROJECT_TYPE = project_type\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m     \u001b[38;5;28mself\u001b[39m.PROJECT_NAME = \u001b[43mproject_name\u001b[49m\n\u001b[32m      8\u001b[39m     \u001b[38;5;28mself\u001b[39m.SOURCE_FILE = source_file\n\u001b[32m      9\u001b[39m     \u001b[38;5;28mself\u001b[39m.TARGET_FILE = target_file\n", "\u001b[31mNameError\u001b[39m: name 'project_name' is not defined"]}], "source": ["class ICPConfig:\n", "    \"\"\"Configuration parameters for ICP alignment.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # Use Papermill parameters\n", "        self.PROJECT_TYPE = project_type\n", "        self.PROJECT_NAME = project_name\n", "        self.SOURCE_FILE = source_file\n", "        self.TARGET_FILE = target_file\n", "        self.MAX_ITERATIONS = max_iterations\n", "        self.TOLERANCE = tolerance\n", "        self.VOXEL_SIZE = voxel_size\n", "        self.DISTANCE_THRESHOLD = distance_threshold\n", "        \n", "        # Setup paths\n", "        self.base_path = Path('../..')\n", "        self.data_path = self.base_path / 'data'\n", "        self.input_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'preprocessing'\n", "        self.output_path = Path(output_dir) / self.PROJECT_TYPE / self.PROJECT_NAME / 'icp_results'\n", "        self.output_path.mkdir(parents=True, exist_ok=True)\n", "        \n", "        print(f\"Project: {self.PROJECT_TYPE}/{self.PROJECT_NAME}\")\n", "        print(f\"Input path: {self.input_path}\")\n", "        print(f\"Output path: {self.output_path}\")\n", "        print(f\"Source file: {self.SOURCE_FILE}\")\n", "        print(f\"Target file: {self.TARGET_FILE}\")\n", "\n", "config = ICPConfig()\n", "\n", "# Initialize MLflow if available\n", "if MLFLOW_AVAILABLE:\n", "    mlflow.set_experiment(mlflow_experiment_name)\n", "    mlflow.start_run(run_name=mlflow_run_name)\n", "    \n", "    # Log parameters\n", "    mlflow.log_param(\"project_type\", project_type)\n", "    mlflow.log_param(\"project_name\", project_name)\n", "    mlflow.log_param(\"source_file\", source_file)\n", "    mlflow.log_param(\"target_file\", target_file)\n", "    mlflow.log_param(\"max_iterations\", max_iterations)\n", "    mlflow.log_param(\"tolerance\", tolerance)\n", "    mlflow.log_param(\"voxel_size\", voxel_size)\n", "    mlflow.log_param(\"distance_threshold\", distance_threshold)\n", "    mlflow.log_param(\"method\", \"ICP\")\n", "    mlflow.log_param(\"stage\", \"alignment\")\n", "    \n", "    print(\"MLflow experiment initialized\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Loading Functions\n", "\n", "Implement functions to load point clouds from various formats."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_las_file(filename, num_points=None):\n", "    \"\"\"\n", "    Reads a LAS file and returns the points as a numpy array.\n", "    \"\"\"\n", "    logger.info(f\"Reading LAS file: {filename}\")\n", "    try:\n", "        las_data = laspy.read(filename)\n", "        \n", "        # Determine the number of points to read\n", "        if num_points is None:\n", "            num_points = len(las_data.x)\n", "        else:\n", "            num_points = min(num_points, len(las_data.x))\n", "        \n", "        # Extract XYZ coordinates\n", "        x = np.array(las_data.x[:num_points], dtype=np.float64)\n", "        y = np.array(las_data.y[:num_points], dtype=np.float64)\n", "        z = np.array(las_data.z[:num_points], dtype=np.float64)\n", "        \n", "        # Stack XYZ coordinates\n", "        points = np.column_stack((x, y, z))\n", "        logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "        return points\n", "    except Exception as e:\n", "        logger.error(f\"Error reading LAS file '{filename}': {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_point_cloud_file(filename):\n", "    \"\"\"\n", "    Reads point cloud from various formats (PLY, PCD, OBJ).\n", "    \"\"\"\n", "    logger.info(f\"Reading point cloud file: {filename}\")\n", "    try:\n", "        file_path = Path(filename)\n", "        \n", "        if file_path.suffix.lower() == '.las':\n", "            return read_las_file(filename)\n", "        elif file_path.suffix.lower() in ['.ply', '.pcd']:\n", "            pcd = o3d.io.read_point_cloud(str(filename))\n", "            points = np.asarray(pcd.points)\n", "            logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "            return points\n", "        elif file_path.suffix.lower() == '.obj':\n", "            mesh = o3d.io.read_triangle_mesh(str(filename))\n", "            points = np.asarray(mesh.vertices)\n", "            logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "            return points\n", "        else:\n", "            logger.error(f\"Unsupported file format: {file_path.suffix}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error reading point cloud file '{filename}': {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Point Cloud Preprocessing\n", "\n", "Implement preprocessing functions for normalization and downsampling."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def normalize_point_cloud(points):\n", "    \"\"\"\n", "    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.\n", "    \"\"\"\n", "    # Center the point cloud at the origin\n", "    centroid = np.mean(points, axis=0)\n", "    centered = points - centroid\n", "    \n", "    # Scale the point cloud to fit inside a unit sphere\n", "    furthest_distance = np.max(np.linalg.norm(centered, axis=1))\n", "    if furthest_distance > 0:\n", "        normalized = centered / furthest_distance\n", "    else:\n", "        normalized = centered\n", "    \n", "    return normalized, centroid, furthest_distance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def downsample_voxel(points, voxel_size=0.02):\n", "    \"\"\"\n", "    Applies voxel grid downsampling to reduce point count while preserving structure.\n", "    \"\"\"\n", "    # Convert numpy array to Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Apply voxel downsampling\n", "    downsampled = pcd.voxel_down_sample(voxel_size=voxel_size)\n", "    \n", "    # Convert back to numpy array\n", "    return np.asarray(downsampled.points)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. ICP Algorithm Implementation\n", "\n", "Implement the core ICP algorithm with multiple variants."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def nearest_neighbor(source, target):\n", "    \"\"\"\n", "    Find nearest neighbors between source and target point clouds.\n", "    \"\"\"\n", "    tree = cKDTree(target)\n", "    distances, indices = tree.query(source)\n", "    return distances, indices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def best_fit_transform(source, target):\n", "    \"\"\"\n", "    Calculates the least-squares best-fit transform between corresponding 3D points.\n", "    \"\"\"\n", "    assert source.shape == target.shape, \"Source and target must have the same shape\"\n", "    \n", "    # Center both point clouds\n", "    source_centroid = np.mean(source, axis=0)\n", "    target_centroid = np.mean(target, axis=0)\n", "    source_centered = source - source_centroid\n", "    target_centered = target - target_centroid\n", "    \n", "    # Compute covariance matrix H\n", "    H = np.dot(source_centered.T, target_centered)\n", "    \n", "    # Singular Value Decomposition\n", "    U, S, Vt = np.linalg.svd(H)\n", "    \n", "    # Compute rotation matrix R\n", "    R = np.dot(Vt.T, U.T)\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = np.dot(Vt.T, U.T)\n", "    \n", "    # Compute translation\n", "    t = target_centroid - np.dot(R, source_centroid)\n", "    \n", "    # Create homogeneous transformation matrix\n", "    T = np.identity(4)\n", "    T[:3, :3] = R\n", "    T[:3, 3] = t\n", "    \n", "    return T, R, t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def icp_algorithm(source, target, max_iterations=50, tolerance=1e-6, verbose=False):\n", "    \"\"\"\n", "    Iterative Closest Point (ICP) algorithm for point cloud alignment.\n", "    \"\"\"\n", "    # Make a copy of the source point cloud\n", "    source_copy = np.copy(source)\n", "    prev_error = float('inf')\n", "    convergence_history = []\n", "    \n", "    # Initialize transformation matrix\n", "    T_combined = np.identity(4)\n", "    \n", "    start_time = time.time()\n", "    \n", "    for iteration in range(max_iterations):\n", "        # Find nearest neighbors\n", "        distances, indices = nearest_neighbor(source_copy, target)\n", "        \n", "        # Compute mean squared error\n", "        mean_error = np.mean(distances**2)\n", "        convergence_history.append(mean_error)\n", "        \n", "        # Check for convergence\n", "        if verbose:\n", "            print(f\"Iteration {iteration+1:3d}, MSE: {mean_error:.10f}\")\n", "        \n", "        if abs(prev_error - mean_error) < tolerance:\n", "            if verbose:\n", "                print(f\"Converged after {iteration+1} iterations.\")\n", "            break\n", "        \n", "        prev_error = mean_error\n", "        \n", "        # Get corresponding points\n", "        corresponding_target_points = target[indices]\n", "        \n", "        # Compute transformation\n", "        T, R, t = best_fit_transform(source_copy, corresponding_target_points)\n", "        \n", "        # Update transformation matrix\n", "        T_combined = np.dot(T, T_combined)\n", "        \n", "        # Apply transformation\n", "        source_copy = np.dot(source_copy, R.T) + t\n", "    \n", "    end_time = time.time()\n", "    \n", "    if verbose:\n", "        print(f\"ICP completed in {end_time - start_time:.4f} seconds\")\n", "        if iteration == max_iterations - 1:\n", "            print(f\"Warning: Maximum iterations ({max_iterations}) reached without convergence.\")\n", "    \n", "    return T_combined, source_copy, mean_error, iteration + 1, convergence_history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Execute ICP Alignment\n", "\n", "Load data and execute the ICP alignment algorithm."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load source and target point clouds\n", "print(\"Loading point cloud data...\")\n", "\n", "# Try to load specified files\n", "source_path = config.input_path / config.SOURCE_FILE\n", "target_path = config.input_path / config.TARGET_FILE\n", "\n", "if source_path.exists() and target_path.exists():\n", "    print(f\"Loading source: {source_path}\")\n", "    source_points = read_point_cloud_file(source_path)\n", "    \n", "    print(f\"Loading target: {target_path}\")\n", "    target_points = read_point_cloud_file(target_path)\n", "    \n", "    if source_points is None or target_points is None:\n", "        raise ValueError(\"Failed to load specified point cloud files\")\n", "        \n", "else:\n", "    print(\"Specified files not found. Creating synthetic test data...\")\n", "    # Create synthetic point clouds for testing\n", "    np.random.seed(42)\n", "    source_points = np.random.rand(5000, 3) * 2 - 1  # Random points in [-1, 1]\n", "    \n", "    # Create target by applying a known transformation\n", "    angle = np.radians(15)\n", "    R_true = np.array([\n", "        [np.cos(angle), -np.sin(angle), 0],\n", "        [np.sin(angle), np.cos(angle), 0],\n", "        [0, 0, 1]\n", "    ])\n", "    t_true = np.array([0.2, 0.1, 0.05])\n", "    target_points = np.dot(source_points, R_true.T) + t_true\n", "    \n", "    # Add some noise\n", "    target_points += np.random.normal(0, 0.01, target_points.shape)\n", "\n", "print(f\"Source points shape: {source_points.shape}\")\n", "print(f\"Target points shape: {target_points.shape}\")\n", "\n", "# Preprocess point clouds\n", "print(\"\\nPreprocessing point clouds...\")\n", "if config.VOXEL_SIZE > 0:\n", "    print(f\"Downsampling with voxel size: {config.VOXEL_SIZE}\")\n", "    source_downsampled = downsample_voxel(source_points, config.VOXEL_SIZE)\n", "    target_downsampled = downsample_voxel(target_points, config.VOXEL_SIZE)\n", "    print(f\"Downsampled source: {source_downsampled.shape[0]} points\")\n", "    print(f\"Downsampled target: {target_downsampled.shape[0]} points\")\n", "else:\n", "    source_downsampled = source_points\n", "    target_downsampled = target_points\n", "\n", "# Execute ICP alignment\n", "print(\"\\n=== Executing ICP Alignment ===\")\n", "icp_start_time = time.time()\n", "\n", "T_icp, aligned_source, final_error, iterations, convergence_history = icp_algorithm(\n", "    source_downsampled, \n", "    target_downsampled, \n", "    max_iterations=config.MAX_ITERATIONS, \n", "    tolerance=config.TOLERANCE, \n", "    verbose=True\n", ")\n", "\n", "icp_time = time.time() - icp_start_time\n", "\n", "print(f\"\\n=== ICP Results ===\")\n", "print(f\"Execution time: {icp_time:.4f} seconds\")\n", "print(f\"Iterations: {iterations}\")\n", "print(f\"Final MSE: {final_error:.10f}\")\n", "print(f\"Converged: {iterations < config.MAX_ITERATIONS}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Performance Evaluation and Results Export\n", "\n", "Calculate comprehensive performance metrics and save results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate comprehensive performance metrics\n", "tree = cKDTree(target_downsampled)\n", "distances, _ = tree.query(aligned_source)\n", "\n", "# Performance metrics\n", "rmse = np.sqrt(np.mean(distances**2))\n", "mean_distance = np.mean(distances)\n", "median_distance = np.median(distances)\n", "std_distance = np.std(distances)\n", "max_distance = np.max(distances)\n", "min_distance = np.min(distances)\n", "\n", "# Accuracy metrics\n", "accuracy_01 = np.mean(distances < 0.01) * 100\n", "accuracy_05 = np.mean(distances < 0.05) * 100\n", "accuracy_10 = np.mean(distances < config.DISTANCE_THRESHOLD) * 100\n", "\n", "print(f\"\\n=== Performance Metrics ===\")\n", "print(f\"RMSE: {rmse:.6f}\")\n", "print(f\"Mean Distance: {mean_distance:.6f}\")\n", "print(f\"Median Distance: {median_distance:.6f}\")\n", "print(f\"Std Distance: {std_distance:.6f}\")\n", "print(f\"Max Distance: {max_distance:.6f}\")\n", "print(f\"Min Distance: {min_distance:.6f}\")\n", "print(f\"Accuracy (< 0.01): {accuracy_01:.2f}%\")\n", "print(f\"Accuracy (< 0.05): {accuracy_05:.2f}%\")\n", "print(f\"Accuracy (< {config.DISTANCE_THRESHOLD}): {accuracy_10:.2f}%\")\n", "\n", "# Apply transformation to full resolution source if downsampling was used\n", "if config.VOXEL_SIZE > 0:\n", "    R_icp = T_icp[:3, :3]\n", "    t_icp = T_icp[:3, 3]\n", "    aligned_source_full = np.dot(source_points, R_icp.T) + t_icp\n", "else:\n", "    aligned_source_full = aligned_source\n", "\n", "# Save aligned point cloud\n", "aligned_pcd = o3d.geometry.PointCloud()\n", "aligned_pcd.points = o3d.utility.Vector3dVector(aligned_source_full)\n", "\n", "output_file_pcd = config.output_path / 'icp_aligned_source.pcd'\n", "output_file_ply = config.output_path / 'icp_aligned_source.ply'\n", "\n", "o3d.io.write_point_cloud(str(output_file_pcd), aligned_pcd)\n", "o3d.io.write_point_cloud(str(output_file_ply), aligned_pcd)\n", "\n", "print(f\"\\nSaved aligned point cloud to:\")\n", "print(f\"  PCD: {output_file_pcd}\")\n", "print(f\"  PLY: {output_file_ply}\")\n", "\n", "# Save metadata\n", "metadata = {\n", "    'method': 'ICP',\n", "    'timestamp': datetime.now().isoformat(),\n", "    'project_type': config.PROJECT_TYPE,\n", "    'project_name': config.PROJECT_NAME,\n", "    'source_file': config.SOURCE_FILE,\n", "    'target_file': config.TARGET_FILE,\n", "    'source_points_count': len(source_points),\n", "    'target_points_count': len(target_points),\n", "    'aligned_points_count': len(aligned_source_full),\n", "    'transformation_matrix': T_icp.tolist(),\n", "    'parameters': {\n", "        'max_iterations': config.MAX_ITERATIONS,\n", "        'tolerance': config.TOLERANCE,\n", "        'voxel_size': config.VOXEL_SIZE,\n", "        'distance_threshold': config.DISTANCE_THRESHOLD\n", "    },\n", "    'performance_metrics': {\n", "        'rmse': rmse,\n", "        'mean_distance': mean_distance,\n", "        'median_distance': median_distance,\n", "        'std_distance': std_distance,\n", "        'max_distance': max_distance,\n", "        'min_distance': min_distance,\n", "        'accuracy_01': accuracy_01,\n", "        'accuracy_05': accuracy_05,\n", "        'accuracy_10': accuracy_10,\n", "        'final_mse': final_error,\n", "        'iterations': iterations,\n", "        'converged': iterations < config.MAX_ITERATIONS\n", "    },\n", "    'timing_info': {\n", "        'total_time': icp_time,\n", "        'alignment_time': icp_time\n", "    },\n", "    'convergence_history': convergence_history\n", "}\n", "\n", "metadata_file = config.output_path / 'icp_alignment_metadata.json'\n", "with open(metadata_file, 'w') as f:\n", "    json.dump(metadata, f, indent=2)\n", "\n", "print(f\"Saved metadata to: {metadata_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. MLflow Logging and Experiment Tracking\n", "\n", "Log results to MLflow for experiment tracking and comparison."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MLflow logging\n", "if MLFLOW_AVAILABLE:\n", "    print(\"\\nLogging results to MLflow...\")\n", "    \n", "    try:\n", "        # Log metrics\n", "        mlflow.log_metric(\"rmse\", rmse)\n", "        mlflow.log_metric(\"mean_distance\", mean_distance)\n", "        mlflow.log_metric(\"median_distance\", median_distance)\n", "        mlflow.log_metric(\"std_distance\", std_distance)\n", "        mlflow.log_metric(\"max_distance\", max_distance)\n", "        mlflow.log_metric(\"min_distance\", min_distance)\n", "        mlflow.log_metric(\"accuracy_01\", accuracy_01)\n", "        mlflow.log_metric(\"accuracy_05\", accuracy_05)\n", "        mlflow.log_metric(\"accuracy_10\", accuracy_10)\n", "        mlflow.log_metric(\"final_mse\", final_error)\n", "        mlflow.log_metric(\"iterations\", iterations)\n", "        mlflow.log_metric(\"execution_time\", icp_time)\n", "        mlflow.log_metric(\"converged\", int(iterations < config.MAX_ITERATIONS))\n", "        mlflow.log_metric(\"source_points_count\", len(source_points))\n", "        mlflow.log_metric(\"target_points_count\", len(target_points))\n", "        \n", "        # Log artifacts\n", "        mlflow.log_artifact(str(output_file_pcd))\n", "        mlflow.log_artifact(str(output_file_ply))\n", "        mlflow.log_artifact(str(metadata_file))\n", "        \n", "        # Add tags\n", "        mlflow.set_tag(\"method\", \"ICP\")\n", "        mlflow.set_tag(\"stage\", \"alignment\")\n", "        mlflow.set_tag(\"project_type\", config.PROJECT_TYPE)\n", "        mlflow.set_tag(\"project_name\", config.PROJECT_NAME)\n", "        mlflow.set_tag(\"data_source\", \"point_cloud_alignment\")\n", "        \n", "        print(\"MLflow logging completed successfully.\")\n", "        \n", "    except Exception as e:\n", "        print(f\"MLflow logging failed: {e}\")\n", "    \n", "    finally:\n", "        # End the MLflow run\n", "        mlflow.end_run()\n", "        print(\"MLflow run ended.\")\n", "else:\n", "    print(\"MLflow not available - skipping experiment tracking.\")\n", "\n", "print(\"\\n=== ICP Alignment Completed Successfully ===\")\n", "print(f\"Results saved to: {config.output_path}\")\n", "print(f\"Execution time: {icp_time:.4f} seconds\")\n", "print(f\"Final RMSE: {rmse:.6f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}