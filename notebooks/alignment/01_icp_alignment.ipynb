# Papermill parameters - can be overridden during execution
project_type = "foundation_analysis"  # Project type for data organization
site_name = "castro_area4"            # Site identifier for processing
source_file = "source_pointcloud.las"  # Source point cloud file
target_file = "target_pointcloud.las"  # Target point cloud file
max_iterations = 50
tolerance = 1e-6
voxel_size = 0.02
distance_threshold = 0.1
output_dir = "../../data/output_runs"
mlflow_experiment_name = "alignment_icp"
mlflow_run_name = f"icp_{project_type}_{site_name}"
enable_visualization = True
save_intermediate = True

# Install required packages
!pip install open3d matplotlib laspy transforms3d scipy pandas mlflow

# Import libraries
import numpy as np
import os
import matplotlib.pyplot as plt
import open3d as o3d
import laspy
import logging
import time
from pathlib import Path
from datetime import datetime
from scipy.spatial import cKDTree
import pandas as pd
import json

# MLflow tracking
try:
    import mlflow
    import mlflow.sklearn
    MLFLOW_AVAILABLE = True
    print("MLflow available for experiment tracking")
except ImportError:
    MLFLOW_AVAILABLE = False
    print("MLflow not available - install with: pip install mlflow")

# Set random seeds for reproducibility
np.random.seed(42)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("ICP Alignment Environment Initialized")
print(f"Open3D version: {o3d.__version__}")
print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

class ICPConfig:
    """Configuration parameters for ICP alignment."""
    
    def __init__(self):
        # Use Papermill parameters
        self.PROJECT_TYPE = project_type
        self.PROJECT_NAME = project_name
        self.SOURCE_FILE = source_file
        self.TARGET_FILE = target_file
        self.MAX_ITERATIONS = max_iterations
        self.TOLERANCE = tolerance
        self.VOXEL_SIZE = voxel_size
        self.DISTANCE_THRESHOLD = distance_threshold
        
        # Setup paths
        self.base_path = Path('../..')
        self.data_path = self.base_path / 'data'
        self.input_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'preprocessing'
        self.output_path = Path(output_dir) / self.PROJECT_TYPE / self.PROJECT_NAME / 'icp_results'
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"Project: {self.PROJECT_TYPE}/{self.PROJECT_NAME}")
        print(f"Input path: {self.input_path}")
        print(f"Output path: {self.output_path}")
        print(f"Source file: {self.SOURCE_FILE}")
        print(f"Target file: {self.TARGET_FILE}")

config = ICPConfig()

# Initialize MLflow if available
if MLFLOW_AVAILABLE:
    mlflow.set_experiment(mlflow_experiment_name)
    mlflow.start_run(run_name=mlflow_run_name)
    
    # Log parameters
    mlflow.log_param("project_type", project_type)
    mlflow.log_param("project_name", project_name)
    mlflow.log_param("source_file", source_file)
    mlflow.log_param("target_file", target_file)
    mlflow.log_param("max_iterations", max_iterations)
    mlflow.log_param("tolerance", tolerance)
    mlflow.log_param("voxel_size", voxel_size)
    mlflow.log_param("distance_threshold", distance_threshold)
    mlflow.log_param("method", "ICP")
    mlflow.log_param("stage", "alignment")
    
    print("MLflow experiment initialized")

def read_las_file(filename, num_points=None):
    """
    Reads a LAS file and returns the points as a numpy array.
    """
    logger.info(f"Reading LAS file: {filename}")
    try:
        las_data = laspy.read(filename)
        
        # Determine the number of points to read
        if num_points is None:
            num_points = len(las_data.x)
        else:
            num_points = min(num_points, len(las_data.x))
        
        # Extract XYZ coordinates
        x = np.array(las_data.x[:num_points], dtype=np.float64)
        y = np.array(las_data.y[:num_points], dtype=np.float64)
        z = np.array(las_data.z[:num_points], dtype=np.float64)
        
        # Stack XYZ coordinates
        points = np.column_stack((x, y, z))
        logger.info(f"Loaded {points.shape[0]} points from {filename}")
        return points
    except Exception as e:
        logger.error(f"Error reading LAS file '{filename}': {e}")
        return None

def read_point_cloud_file(filename):
    """
    Reads point cloud from various formats (PLY, PCD, OBJ).
    """
    logger.info(f"Reading point cloud file: {filename}")
    try:
        file_path = Path(filename)
        
        if file_path.suffix.lower() == '.las':
            return read_las_file(filename)
        elif file_path.suffix.lower() in ['.ply', '.pcd']:
            pcd = o3d.io.read_point_cloud(str(filename))
            points = np.asarray(pcd.points)
            logger.info(f"Loaded {points.shape[0]} points from {filename}")
            return points
        elif file_path.suffix.lower() == '.obj':
            mesh = o3d.io.read_triangle_mesh(str(filename))
            points = np.asarray(mesh.vertices)
            logger.info(f"Loaded {points.shape[0]} points from {filename}")
            return points
        else:
            logger.error(f"Unsupported file format: {file_path.suffix}")
            return None
    except Exception as e:
        logger.error(f"Error reading point cloud file '{filename}': {e}")
        return None

def normalize_point_cloud(points):
    """
    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.
    """
    # Center the point cloud at the origin
    centroid = np.mean(points, axis=0)
    centered = points - centroid
    
    # Scale the point cloud to fit inside a unit sphere
    furthest_distance = np.max(np.linalg.norm(centered, axis=1))
    if furthest_distance > 0:
        normalized = centered / furthest_distance
    else:
        normalized = centered
    
    return normalized, centroid, furthest_distance

def downsample_voxel(points, voxel_size=0.02):
    """
    Applies voxel grid downsampling to reduce point count while preserving structure.
    """
    # Convert numpy array to Open3D point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    
    # Apply voxel downsampling
    downsampled = pcd.voxel_down_sample(voxel_size=voxel_size)
    
    # Convert back to numpy array
    return np.asarray(downsampled.points)

def nearest_neighbor(source, target):
    """
    Find nearest neighbors between source and target point clouds.
    """
    tree = cKDTree(target)
    distances, indices = tree.query(source)
    return distances, indices

def best_fit_transform(source, target):
    """
    Calculates the least-squares best-fit transform between corresponding 3D points.
    """
    assert source.shape == target.shape, "Source and target must have the same shape"
    
    # Center both point clouds
    source_centroid = np.mean(source, axis=0)
    target_centroid = np.mean(target, axis=0)
    source_centered = source - source_centroid
    target_centered = target - target_centroid
    
    # Compute covariance matrix H
    H = np.dot(source_centered.T, target_centered)
    
    # Singular Value Decomposition
    U, S, Vt = np.linalg.svd(H)
    
    # Compute rotation matrix R
    R = np.dot(Vt.T, U.T)
    
    # Ensure proper rotation (det(R) = 1)
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)
    
    # Compute translation
    t = target_centroid - np.dot(R, source_centroid)
    
    # Create homogeneous transformation matrix
    T = np.identity(4)
    T[:3, :3] = R
    T[:3, 3] = t
    
    return T, R, t

def icp_algorithm(source, target, max_iterations=50, tolerance=1e-6, verbose=False):
    """
    Iterative Closest Point (ICP) algorithm for point cloud alignment.
    """
    # Make a copy of the source point cloud
    source_copy = np.copy(source)
    prev_error = float('inf')
    convergence_history = []
    
    # Initialize transformation matrix
    T_combined = np.identity(4)
    
    start_time = time.time()
    
    for iteration in range(max_iterations):
        # Find nearest neighbors
        distances, indices = nearest_neighbor(source_copy, target)
        
        # Compute mean squared error
        mean_error = np.mean(distances**2)
        convergence_history.append(mean_error)
        
        # Check for convergence
        if verbose:
            print(f"Iteration {iteration+1:3d}, MSE: {mean_error:.10f}")
        
        if abs(prev_error - mean_error) < tolerance:
            if verbose:
                print(f"Converged after {iteration+1} iterations.")
            break
        
        prev_error = mean_error
        
        # Get corresponding points
        corresponding_target_points = target[indices]
        
        # Compute transformation
        T, R, t = best_fit_transform(source_copy, corresponding_target_points)
        
        # Update transformation matrix
        T_combined = np.dot(T, T_combined)
        
        # Apply transformation
        source_copy = np.dot(source_copy, R.T) + t
    
    end_time = time.time()
    
    if verbose:
        print(f"ICP completed in {end_time - start_time:.4f} seconds")
        if iteration == max_iterations - 1:
            print(f"Warning: Maximum iterations ({max_iterations}) reached without convergence.")
    
    return T_combined, source_copy, mean_error, iteration + 1, convergence_history

# Load source and target point clouds
print("Loading point cloud data...")

# Try to load specified files
source_path = config.input_path / config.SOURCE_FILE
target_path = config.input_path / config.TARGET_FILE

if source_path.exists() and target_path.exists():
    print(f"Loading source: {source_path}")
    source_points = read_point_cloud_file(source_path)
    
    print(f"Loading target: {target_path}")
    target_points = read_point_cloud_file(target_path)
    
    if source_points is None or target_points is None:
        raise ValueError("Failed to load specified point cloud files")
        
else:
    print("Specified files not found. Creating synthetic test data...")
    # Create synthetic point clouds for testing
    np.random.seed(42)
    source_points = np.random.rand(5000, 3) * 2 - 1  # Random points in [-1, 1]
    
    # Create target by applying a known transformation
    angle = np.radians(15)
    R_true = np.array([
        [np.cos(angle), -np.sin(angle), 0],
        [np.sin(angle), np.cos(angle), 0],
        [0, 0, 1]
    ])
    t_true = np.array([0.2, 0.1, 0.05])
    target_points = np.dot(source_points, R_true.T) + t_true
    
    # Add some noise
    target_points += np.random.normal(0, 0.01, target_points.shape)

print(f"Source points shape: {source_points.shape}")
print(f"Target points shape: {target_points.shape}")

# Preprocess point clouds
print("\nPreprocessing point clouds...")
if config.VOXEL_SIZE > 0:
    print(f"Downsampling with voxel size: {config.VOXEL_SIZE}")
    source_downsampled = downsample_voxel(source_points, config.VOXEL_SIZE)
    target_downsampled = downsample_voxel(target_points, config.VOXEL_SIZE)
    print(f"Downsampled source: {source_downsampled.shape[0]} points")
    print(f"Downsampled target: {target_downsampled.shape[0]} points")
else:
    source_downsampled = source_points
    target_downsampled = target_points

# Execute ICP alignment
print("\n=== Executing ICP Alignment ===")
icp_start_time = time.time()

T_icp, aligned_source, final_error, iterations, convergence_history = icp_algorithm(
    source_downsampled, 
    target_downsampled, 
    max_iterations=config.MAX_ITERATIONS, 
    tolerance=config.TOLERANCE, 
    verbose=True
)

icp_time = time.time() - icp_start_time

print(f"\n=== ICP Results ===")
print(f"Execution time: {icp_time:.4f} seconds")
print(f"Iterations: {iterations}")
print(f"Final MSE: {final_error:.10f}")
print(f"Converged: {iterations < config.MAX_ITERATIONS}")

# Calculate comprehensive performance metrics
tree = cKDTree(target_downsampled)
distances, _ = tree.query(aligned_source)

# Performance metrics
rmse = np.sqrt(np.mean(distances**2))
mean_distance = np.mean(distances)
median_distance = np.median(distances)
std_distance = np.std(distances)
max_distance = np.max(distances)
min_distance = np.min(distances)

# Accuracy metrics
accuracy_01 = np.mean(distances < 0.01) * 100
accuracy_05 = np.mean(distances < 0.05) * 100
accuracy_10 = np.mean(distances < config.DISTANCE_THRESHOLD) * 100

print(f"\n=== Performance Metrics ===")
print(f"RMSE: {rmse:.6f}")
print(f"Mean Distance: {mean_distance:.6f}")
print(f"Median Distance: {median_distance:.6f}")
print(f"Std Distance: {std_distance:.6f}")
print(f"Max Distance: {max_distance:.6f}")
print(f"Min Distance: {min_distance:.6f}")
print(f"Accuracy (< 0.01): {accuracy_01:.2f}%")
print(f"Accuracy (< 0.05): {accuracy_05:.2f}%")
print(f"Accuracy (< {config.DISTANCE_THRESHOLD}): {accuracy_10:.2f}%")

# Apply transformation to full resolution source if downsampling was used
if config.VOXEL_SIZE > 0:
    R_icp = T_icp[:3, :3]
    t_icp = T_icp[:3, 3]
    aligned_source_full = np.dot(source_points, R_icp.T) + t_icp
else:
    aligned_source_full = aligned_source

# Save aligned point cloud
aligned_pcd = o3d.geometry.PointCloud()
aligned_pcd.points = o3d.utility.Vector3dVector(aligned_source_full)

output_file_pcd = config.output_path / 'icp_aligned_source.pcd'
output_file_ply = config.output_path / 'icp_aligned_source.ply'

o3d.io.write_point_cloud(str(output_file_pcd), aligned_pcd)
o3d.io.write_point_cloud(str(output_file_ply), aligned_pcd)

print(f"\nSaved aligned point cloud to:")
print(f"  PCD: {output_file_pcd}")
print(f"  PLY: {output_file_ply}")

# Save metadata
metadata = {
    'method': 'ICP',
    'timestamp': datetime.now().isoformat(),
    'project_type': config.PROJECT_TYPE,
    'project_name': config.PROJECT_NAME,
    'source_file': config.SOURCE_FILE,
    'target_file': config.TARGET_FILE,
    'source_points_count': len(source_points),
    'target_points_count': len(target_points),
    'aligned_points_count': len(aligned_source_full),
    'transformation_matrix': T_icp.tolist(),
    'parameters': {
        'max_iterations': config.MAX_ITERATIONS,
        'tolerance': config.TOLERANCE,
        'voxel_size': config.VOXEL_SIZE,
        'distance_threshold': config.DISTANCE_THRESHOLD
    },
    'performance_metrics': {
        'rmse': rmse,
        'mean_distance': mean_distance,
        'median_distance': median_distance,
        'std_distance': std_distance,
        'max_distance': max_distance,
        'min_distance': min_distance,
        'accuracy_01': accuracy_01,
        'accuracy_05': accuracy_05,
        'accuracy_10': accuracy_10,
        'final_mse': final_error,
        'iterations': iterations,
        'converged': iterations < config.MAX_ITERATIONS
    },
    'timing_info': {
        'total_time': icp_time,
        'alignment_time': icp_time
    },
    'convergence_history': convergence_history
}

metadata_file = config.output_path / 'icp_alignment_metadata.json'
with open(metadata_file, 'w') as f:
    json.dump(metadata, f, indent=2)

print(f"Saved metadata to: {metadata_file}")

# MLflow logging
if MLFLOW_AVAILABLE:
    print("\nLogging results to MLflow...")
    
    try:
        # Log metrics
        mlflow.log_metric("rmse", rmse)
        mlflow.log_metric("mean_distance", mean_distance)
        mlflow.log_metric("median_distance", median_distance)
        mlflow.log_metric("std_distance", std_distance)
        mlflow.log_metric("max_distance", max_distance)
        mlflow.log_metric("min_distance", min_distance)
        mlflow.log_metric("accuracy_01", accuracy_01)
        mlflow.log_metric("accuracy_05", accuracy_05)
        mlflow.log_metric("accuracy_10", accuracy_10)
        mlflow.log_metric("final_mse", final_error)
        mlflow.log_metric("iterations", iterations)
        mlflow.log_metric("execution_time", icp_time)
        mlflow.log_metric("converged", int(iterations < config.MAX_ITERATIONS))
        mlflow.log_metric("source_points_count", len(source_points))
        mlflow.log_metric("target_points_count", len(target_points))
        
        # Log artifacts
        mlflow.log_artifact(str(output_file_pcd))
        mlflow.log_artifact(str(output_file_ply))
        mlflow.log_artifact(str(metadata_file))
        
        # Add tags
        mlflow.set_tag("method", "ICP")
        mlflow.set_tag("stage", "alignment")
        mlflow.set_tag("project_type", config.PROJECT_TYPE)
        mlflow.set_tag("project_name", config.PROJECT_NAME)
        mlflow.set_tag("data_source", "point_cloud_alignment")
        
        print("MLflow logging completed successfully.")
        
    except Exception as e:
        print(f"MLflow logging failed: {e}")
    
    finally:
        # End the MLflow run
        mlflow.end_run()
        print("MLflow run ended.")
else:
    print("MLflow not available - skipping experiment tracking.")

print("\n=== ICP Alignment Completed Successfully ===")
print(f"Results saved to: {config.output_path}")
print(f"Execution time: {icp_time:.4f} seconds")
print(f"Final RMSE: {rmse:.6f}")